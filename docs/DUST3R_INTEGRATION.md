# Dust3R Dataset Integration for VGGT

本文档说明如何在 VGGT 训练中使用 Dust3R 数据集。

## 概述

我们创建了一个适配器 (`Dust3RDatasetAdapter`)，使 VGGT 能够直接使用 Dust3R 的预处理数据进行训练。该适配器：

1. 将 Dust3R 的成对视图格式转换为 VGGT 期望的序列格式
2. 处理坐标系转换和数据类型转换
3. 支持所有 Dust3R 支持的数据集

## 支持的 Dust3R 数据集

- **Co3d**: 通用对象的多视角数据集
- **BlendedMVS**: 合成多视角立体数据集
- **ARKitScenes**: 室内场景数据集
- **ScanNet++**: 室内扫描数据集
- **MegaDepth**: 大规模户外场景数据集
- **Waymo**: 自动驾驶数据集
- **Habitat**: 模拟环境数据集
- **StaticThings3D**: 静态3D对象数据集
- **WildRGBD**: 野外RGB-D数据集

## 快速开始

### 1. 准备 Dust3R 数据

首先确保你已经按照 Dust3R 的说明准备了数据。例如，对于 Co3d 数据集：

```bash
# 按照 dust3r/datasets_preprocess/preprocess_co3d.py 的说明预处理数据
cd dust3r/datasets_preprocess/
python preprocess_co3d.py --co3d_dir /path/to/raw/co3d --output_dir /path/to/processed/co3d
```

### 2. 使用 Dust3R 数据集进行 Finetuning

我们提供了专门的 finetuning 配置文件 `config/dust3r_datasets.yaml`，该配置：
- **只使用 Dust3R 数据集**（不混合原 VGGT 数据集）
- **从预训练模型开始**
- **使用适合 finetuning 的学习率**（2e-5 vs 5e-5）
- **减少训练轮数**（20 epochs vs 默认更多）

```bash
cd vggt/training
python launch.py --config dust3r_datasets
```

**重要**: 确保预训练模型路径正确：
```bash
# 检查预训练模型路径是否存在
ls /caojiaolong/hjd/project/vggt/VGGT-1B
# 如果路径不正确，修改配置文件中的 checkpoint.resume_checkpoint_path
```

### 3. 选择特定数据集进行 Finetuning

默认配置启用了多个 Dust3R 数据集。如果你只想使用特定数据集，可以：

**方法1: 修改配置文件**
编辑 `config/dust3r_datasets.yaml`，注释掉不需要的数据集：
```yaml
dataset_configs:
  # 只保留你需要的数据集，注释掉其他的
  - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
    dust3r_dataset_config: "Co3d(split='train', ROOT='/your/path', resolution=224)"
  
  # - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter  # 注释掉
  #   dust3r_dataset_config: "BlendedMVS(...)"                     # 注释掉
```

**方法2: 命令行覆盖**
```bash
# 只使用 Co3d 数据集
python launch.py --config dust3r_datasets \
    data.train.dataset.dataset_configs='[{_target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter, dust3r_dataset_config: "Co3d(split=\"train\", ROOT=\"/your/path/to/co3d\", resolution=224)", split: train, len_train: 50000, sequence_length: 8}]'
```

### 4. 混合训练（如果需要）

如果仍需要混合原 VGGT 数据集：

```yaml
data:
  train:
    dataset:
      _target_: data.composed_dataset.ComposedDataset
      dataset_configs:
        # 原有的 VGGT 数据集
        - _target_: data.datasets.co3d.Co3dDataset
          split: train
          CO3D_DIR: /path/to/vggt/co3d
        
        # 新增的 Dust3R 数据集
        - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
          dust3r_dataset_config: "Co3d(split='train', ROOT='/path/to/dust3r/co3d', resolution=224)"
          split: train
          len_train: 50000
          sequence_length: 8
```

## 配置参数说明

### Dust3RDatasetAdapter 参数

- `dust3r_dataset_config`: Dust3R 数据集的配置字符串
- `split`: 数据集分割 ('train' 或 'test')
- `len_train`/`len_test`: 训练/测试数据集长度
- `sequence_length`: 每个序列的图像数量

### 常用 Dust3R 数据集配置

```python
# Co3d
"Co3d(split='train', ROOT='/path/to/data', resolution=224, aug_crop=16)"

# BlendedMVS
"BlendedMVS(split='train', ROOT='/path/to/data', resolution=224)"

# ARKitScenes
"ARKitScenes(split='train', ROOT='/path/to/data', resolution=224)"

# ScanNet++
"ScanNetpp(split='train', ROOT='/path/to/data', resolution=224)"

# MegaDepth
"MegaDepth(split='train', ROOT='/path/to/data', resolution=224)"
```

## 混合数据集训练

你可以同时使用多个 Dust3R 数据集和原有的 VGGT 数据集：

```yaml
dataset_configs:
  # VGGT 原生数据集
  - _target_: data.datasets.co3d.Co3dDataset
    split: train
    len_train: 30000
  
  # Dust3R Co3d
  - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
    dust3r_dataset_config: "Co3d(split='train', ROOT='/dust3r/co3d', resolution=224)"
    len_train: 40000
  
  # Dust3R BlendedMVS
  - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
    dust3r_dataset_config: "BlendedMVS(split='train', ROOT='/dust3r/blendedmvs', resolution=224)"
    len_train: 30000
```

## 注意事项

1. **路径配置**: 确保 `ROOT` 路径指向正确的 Dust3R 预处理数据目录
2. **内存使用**: Dust3R 数据集可能较大，注意调整 `num_workers` 和 `max_img_per_gpu`
3. **数据格式**: 适配器会自动处理坐标系转换，无需手动转换
4. **轨迹数据**: Dust3R 数据集不包含轨迹信息，`tracks` 和 `track_masks` 将设为 `None`

## 故障排除

### 常见错误及解决方法

1. **ImportError: No module named 'dust3r'**
   - 确保 dust3r 目录路径正确
   - 检查 `sys.path` 设置

2. **FileNotFoundError: Dataset path not found**
   - 检查 `ROOT` 路径是否正确
   - 确保数据已正确预处理

3. **RuntimeError: No views generated**
   - 检查数据集是否为空
   - 验证 split 参数是否正确

4. **Memory errors**
   - 减少 `sequence_length`
   - 降低 `num_workers`
   - 调整 `max_img_per_gpu`

## 性能优化

1. **数据加载优化**:
   ```yaml
   num_workers: 8  # 根据CPU核心数调整
   max_img_per_gpu: 4  # 根据GPU内存调整
   ```

2. **序列长度优化**:
   ```yaml
   sequence_length: 6  # 较短序列可能更高效
   ```

3. **分辨率优化**:
   ```python
   # 在 dust3r_dataset_config 中调整 resolution
   "Co3d(..., resolution=224)"  # 较小分辨率训练更快
   ```

## 示例训练命令

```bash
# 单个 Dust3R 数据集训练
python launch.py --config-name dust3r_datasets

# 指定特定数据集和路径
python launch.py --config-name dust3r_datasets \
    data.train.dataset.dataset_configs[0].dust3r_dataset_config="BlendedMVS(split='train', ROOT='/data/blendedmvs', resolution=224)"

# 混合训练（需要自定义配置文件）
python launch.py --config-name my_mixed_config
```