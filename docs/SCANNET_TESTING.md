# ScanNet Testing for VGGT Model

This document provides comprehensive instructions for testing the VGGT (Visual Geometry Group Transformer) model on the ScanNet-1500 dataset to reproduce paper results.

## Overview

The ScanNet testing functionality evaluates the pre-trained VGGT model on the ScanNet-1500 test dataset, computing performance metrics for:
- Camera pose estimation
- Depth map prediction  
- 3D point cloud reconstruction quality

## Prerequisites

### System Requirements
- Python 3.8+
- CUDA-capable GPU with at least 8GB VRAM
- 32GB+ system RAM recommended

### Dependencies
All required dependencies are listed in `requirements.txt`. Key packages include:
- PyTorch 1.12+
- torchvision
- numpy
- opencv-python
- matplotlib
- scipy
- hydra-core
- omegaconf

### Data Requirements
- **Pre-trained model**: `/defaultShare/pubdata/3D/ckpt/vggt/model.pt`
- **ScanNet-1500 dataset**: `/defaultShare/pubdata/3D/scannet-1500/LoFTR/testdata/scannet_test_1500/scannet_test_1500`

## Quick Start

### 1. Basic Testing
Run ScanNet testing with default configuration:

```bash
cd vggt
python test_scannet.py
```

### 2. Custom Configuration
Test with custom paths and settings:

```bash
python test_scannet.py \
    --config scannet_test \
    --checkpoint /path/to/your/model.pt \
    --data_dir /path/to/scannet/data \
    --output_dir /path/to/output
```

### 3. Using Training Framework
Alternatively, use the training framework directly:

```bash
cd training
python launch.py --config scannet_test
```

## Configuration

### Main Configuration File
The main configuration is in `training/config/scannet_test.yaml`. Key settings:

```yaml
# Model settings
model:
  _target_: vggt.models.vggt.VGGT
  img_size: 518
  patch_size: 14
  embed_dim: 1024
  enable_camera: True
  enable_depth: True
  enable_point: True

# Data settings
data:
  val:
    dataset:
      dataset_configs:
        - _target_: data.datasets.scannet.ScanNetDataset
          scannet_dir: /path/to/scannet/data
          min_num_images: 8
          max_num_images: 16
          use_depth: True
          use_pose: True

# Checkpoint settings
checkpoint:
  resume_checkpoint_path: /path/to/model.pt
  strict: False
```

### Customizing Configuration
You can override configuration values via command line:

```bash
python test_scannet.py \
    --config scannet_test \
    --checkpoint /custom/path/model.pt \
    --data_dir /custom/path/scannet
```

## Data Format

### Expected ScanNet Directory Structure
```
scannet_test_1500/
├── scene0000_00/
│   ├── color/          # RGB images (.jpg or .png)
│   ├── depth/          # Depth maps (.png, 16-bit, scale 1000)
│   ├── pose/           # Camera poses (.txt, 4x4 matrices)
│   └── intrinsic/
│       └── intrinsic_color.txt  # Camera intrinsics
├── scene0000_01/
│   └── ...
└── ...
```

### Alternative Structure Support
The dataset loader also supports alternative structures:
- `rgb/` instead of `color/`
- `camera-intrinsics.txt` instead of `intrinsic/intrinsic_color.txt`

### Data Validation
Check your data structure:

```python
from training.utils.scannet_utils import check_scannet_data_structure

info = check_scannet_data_structure('/path/to/scannet/data')
print(f"Found {info['total_scenes']} scenes with {info['total_images']} images")
print(f"Issues: {info['structure_issues']}")
```

## Output and Results

### Output Directory Structure
```
output_dir/
├── scannet_test_results.json    # Complete results in JSON format
├── metrics_summary.txt          # Human-readable metrics summary
├── test_log.txt                # Detailed execution log
├── predictions/                # Model predictions per scene
│   ├── scene0000_00/
│   │   ├── camera_poses.npy
│   │   ├── depth/
│   │   │   ├── depth_0000.png
│   │   │   └── ...
│   │   └── points/
│   │       ├── points_0000.ply
│   │       └── ...
│   └── ...
├── visualizations/             # Visualization images
│   ├── scene0000_00/
│   │   ├── frame_0000.png
│   │   └── ...
│   └── ...
└── evaluation_report.html     # Comprehensive HTML report
```

### Key Metrics

#### Camera Pose Estimation
- **Translation Error**: Mean absolute error in meters
- **Rotation Error**: Mean angular error in degrees  
- **Success Rate**: Percentage of poses within error thresholds

#### Depth Estimation
- **Absolute Relative Error**: Mean |pred - gt| / gt
- **RMSE**: Root mean square error
- **Accuracy δ < 1.25**: Percentage of pixels with relative error < 1.25

#### 3D Point Cloud Reconstruction
- **Chamfer Distance**: Bidirectional point-to-point distance
- **F-Score**: Precision-recall harmonic mean at distance thresholds
- **Completeness**: Coverage of ground truth geometry

### Expected Performance
Based on the VGGT paper, expected performance on ScanNet-1500:

| Metric | Expected Value |
|--------|----------------|
| Translation Error | ~0.15m |
| Rotation Error | ~2.5° |
| Depth Abs Rel Error | ~0.12 |
| Chamfer Distance | ~0.025 |

## Advanced Usage

### Custom Scene Selection
Test on specific scenes by creating a scene list file:

```bash
# Create scene_list.txt
echo "scene0000_00" > scene_list.txt
echo "scene0001_00" >> scene_list.txt

# Use in configuration
python test_scannet.py --config scannet_test
```

Then modify the config to use the scene list:
```yaml
data:
  val:
    dataset:
      dataset_configs:
        - scene_list_file: scene_list.txt
```

### Memory Optimization
For systems with limited GPU memory:

```yaml
# Reduce batch size
max_img_per_gpu: 8

# Limit number of images per sequence
data:
  val:
    dataset:
      dataset_configs:
        - max_num_images: 8

# Process fewer scenes for testing
limit_val_batches: 100
```

### Debugging Mode
Enable debug mode for faster testing on a subset:

```yaml
data:
  val:
    common_config:
      debug: True  # Limits to first 3 scenes
```

## Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
**Error**: `RuntimeError: CUDA out of memory`

**Solutions**:
- Reduce `max_img_per_gpu` in config
- Reduce `max_num_images` per sequence
- Use smaller image size (though this may affect accuracy)

#### 2. Data Loading Errors
**Error**: `FileNotFoundError` or `No valid scenes found`

**Solutions**:
- Verify data directory path is correct
- Check data structure matches expected format
- Use `check_scannet_data_structure()` to diagnose issues

#### 3. Model Loading Errors
**Error**: `Missing keys` or `Unexpected keys` in checkpoint

**Solutions**:
- Ensure checkpoint path is correct
- Set `strict: False` in checkpoint config
- Verify model architecture matches checkpoint

#### 4. Import Errors
**Error**: `ModuleNotFoundError`

**Solutions**:
- Ensure you're running from the correct directory
- Check that all dependencies are installed
- Verify Python path includes the project directory

### Performance Optimization

#### Multi-GPU Support
The framework supports multi-GPU testing via DDP:

```bash
# Use multiple GPUs
CUDA_VISIBLE_DEVICES=0,1,2,3 python -m torch.distributed.launch \
    --nproc_per_node=4 test_scannet.py --config scannet_test
```

#### Faster Evaluation
For quicker evaluation during development:

```yaml
# Process fewer batches
limit_val_batches: 50

# Reduce visualization frequency
logging:
  log_visual_frequency:
    val: 100  # Log visuals every 100 batches instead of 50
```

## Integration with Existing Codebase

The ScanNet testing functionality integrates seamlessly with the existing VGGT codebase:

- **Dataset Class**: `training/data/datasets/scannet.py` follows the same pattern as other datasets
- **Configuration**: Uses the same Hydra-based config system
- **Training Framework**: Leverages the existing `Trainer` class for consistency
- **Evaluation**: Extends the existing loss and evaluation infrastructure

## Citation

If you use this ScanNet testing functionality, please cite the VGGT paper:

```bibtex
@article{vggt2024,
  title={Visual Geometry Grounded Transformer},
  author={[Authors]},
  journal={CVPR},
  year={2025}
}
```

## Support

For issues and questions:
1. Check this documentation first
2. Review the troubleshooting section
3. Examine the log files in the output directory
4. Create an issue with detailed error messages and system information
