# RealEstate10K 数据集支持

本指南介绍如何在VGGT框架中使用RealEstate10K数据集进行训练和测试。

## 数据集介绍

RealEstate10K是一个包含80,000+房地产视频的数据集，每个视频包含多个帧，具有已知的相机参数和深度信息。数据集特点：

- **视频数量**: 80,000+ 个视频
- **帧数**: 每个视频8-16帧
- **相机参数**: 包含外参和内参
- **深度信息**: 提供深度图
- **场景类型**: 室内外房地产场景

## 数据集结构

```
realestate10k_data/
├── video_001/
│   ├── frames/
│   │   ├── frame_001.jpg
│   │   ├── frame_002.jpg
│   │   └── ...
│   ├── depths/
│   │   ├── frame_001.png
│   │   ├── frame_002.png
│   │   └── ...
│   ├── poses/
│   │   ├── frame_001.txt
│   │   ├── frame_002.txt
│   │   └── ...
│   └── intrinsics/
│       ├── frame_001.txt
│       ├── frame_002.txt
│       └── ...
├── video_002/
│   └── ...
├── train_videos.txt
├── val_videos.txt
├── test_videos.txt
└── metadata.json
```

## 安装和准备

### 1. 下载数据集

```bash
# 从官方网站下载
# https://google.github.io/realestate10k/
```

### 2. 准备数据集

```bash
cd vggt/training

# 创建数据集分割
python prepare_realestate10k.py --data_root /path/to/realestate10k --create_splits

# 验证数据集结构
python prepare_realestate10k.py --data_root /path/to/realestate10k --validate
```

### 3. 配置路径

编辑 `config/realestate10k.yaml` 文件：

```yaml
data:
  val:
    dataset:
      dataset_configs:
        - _target_: data.datasets.realestate10k.RealEstate10KDataset
          data_root: /path/to/your/realestate10k/data  # 修改为你的路径

checkpoint:
  resume_checkpoint_path: /path/to/your/trained/model.pt  # 修改为你的模型路径
```

## 使用方法

### 1. 测试预训练模型

```bash
cd vggt/training

# 使用RealEstate10K配置测试
python launch.py --config realestate10k
```

### 2. 自定义参数

```bash
# 修改批次大小
python launch.py --config realestate10k --limit_val_batches 50

# 使用自定义配置
python launch.py --config realestate10k --checkpoint /path/to/model.pt
```

## 配置参数说明

### 数据集参数

- `data_root`: 数据集根目录路径
- `split`: 数据集分割 ('train', 'val', 'test')
- `min_num_frames`: 每个序列的最小帧数 (默认: 8)
- `max_num_frames`: 每个序列的最大帧数 (默认: 16)
- `use_depth`: 是否加载深度图 (默认: True)
- `use_pose`: 是否加载相机姿态 (默认: True)

### 训练参数

- `mode`: 运行模式 ('train', 'val')
- `limit_val_batches`: 验证批次数量限制
- `max_img_per_gpu`: 每个GPU的最大图像数量

## 数据集适配器特性

### 1. 自动帧选择

```python
# 随机选择8-16帧进行训练
selected_frames = np.random.choice(frame_files, num_frames, replace=False)
```

### 2. 深度图处理

```python
# 自动加载和处理深度图
if self.load_depth:
    depth_map = read_depth(depth_path, 1.0)
    depth_map = threshold_depth_map(depth_map, min_percentile=-1, max_percentile=98)
```

### 3. 相机参数处理

```python
# 加载相机外参和内参
extrinsic = np.loadtxt(pose_path)  # 3x4 或 4x4 矩阵
intrinsic = np.loadtxt(intrinsic_path)  # 3x3 矩阵
```

### 4. 数据增强

```python
# 继承自BaseDataset的数据增强功能
processed = self.process_one_image(
    image, depth_map, extrinsic, intrinsic,
    original_size, target_shape
)
```

## 性能优化

### 1. 内存优化

```yaml
# 减少每个GPU的图像数量
max_img_per_gpu: 32  # 根据GPU内存调整

# 限制验证批次
limit_val_batches: 100
```

### 2. 数据加载优化

```yaml
# 增加数据加载线程
num_workers: 8

# 启用数据预取
prefetch_factor: 2
```

## 常见问题

### 1. 内存不足

**问题**: 训练时出现OOM错误

**解决方案**:
- 减少 `max_img_per_gpu` 参数
- 减少 `max_num_frames` 参数
- 使用梯度累积

### 2. 数据集路径错误

**问题**: 找不到数据集文件

**解决方案**:
- 检查 `data_root` 路径是否正确
- 确保数据集结构符合要求
- 运行验证脚本检查数据集

### 3. 相机参数缺失

**问题**: 某些帧缺少相机参数

**解决方案**:
- 设置 `use_pose: False` 使用默认参数
- 检查数据集完整性
- 使用数据预处理脚本修复

## 评估指标

RealEstate10K数据集支持以下评估指标：

- **相机姿态误差**: 平移和旋转误差
- **深度重建质量**: 深度图精度
- **几何一致性**: 3D几何重建质量

## 示例输出

```
Loading RealEstate10K dataset from /path/to/realestate10k
Found 1000 valid videos for test split
Dataset length: 10000
Testing model with checkpoint: /path/to/model.pt
Validation completed! Processed 100 batches
```

## 参考链接

- [RealEstate10K 官方网站](https://google.github.io/realestate10k/)
- [VGGT 论文](https://arxiv.org/abs/2303.15862)
- [数据集下载](https://google.github.io/realestate10k/download.html) 