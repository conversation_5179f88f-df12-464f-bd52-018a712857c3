#!/usr/bin/env python3
"""
Test script for ScanNet evaluator to identify runtime errors.
"""

import sys
sys.path.append('.')
import torch
import numpy as np
from training.evaluation.scannet_evaluator import ScanNetEvaluator

def test_evaluator():
    """Test the ScanNet evaluator with mock data."""
    print('Creating evaluator...')
    evaluator = ScanNetEvaluator(save_predictions=False, save_visualizations=False)

    # Create mock data to test the evaluation pipeline
    print('Creating mock data...')
    B, N, H, W = 1, 4, 480, 640
    device = 'cpu'

    # Mock predictions
    predictions = {
        'pose_enc_list': [torch.randn(B, N, 9, device=device)],  # Final stage pose encodings
        'depth': torch.rand(B, N, H, W, device=device) * 5 + 0.1,  # Depth maps
        'world_points': torch.randn(B, N, H, W, 3, device=device) * 10  # 3D points
    }

    # Mock ground truth batch
    batch = {
        'extrinsics': torch.eye(4, device=device).unsqueeze(0).unsqueeze(0).repeat(B, N, 1, 1)[:, :, :3, :],  # (B, N, 3, 4)
        'intrinsics': torch.tensor([[[500, 0, 320], [0, 500, 240], [0, 0, 1]]], device=device).float().unsqueeze(1).repeat(B, N, 1, 1),  # (B, N, 3, 3)
        'depths': torch.rand(B, N, H, W, device=device) * 5 + 0.1,  # Ground truth depths
        'world_points': torch.randn(B, N, H, W, 3, device=device) * 10,  # Ground truth 3D points
        'point_masks': torch.ones(B, N, H, W, device=device, dtype=torch.bool),  # Valid point masks
        'images': torch.randn(B, N, 3, H, W, device=device)  # RGB images
    }

    print('Running evaluation...')
    try:
        results = evaluator.evaluate_batch(predictions, batch, 'test_scene')
        print('Batch evaluation successful!')
        print('Results keys:', list(results.keys()))
        
        # Test final metrics computation
        final_metrics = evaluator.compute_final_metrics()
        print('Final metrics computation successful!')
        print('Final metrics keys:', list(final_metrics.keys()))
        
        return True
        
    except Exception as e:
        print(f'Error during evaluation: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_evaluator()
    if success:
        print('All tests passed!')
    else:
        print('Tests failed!')
        sys.exit(1)
