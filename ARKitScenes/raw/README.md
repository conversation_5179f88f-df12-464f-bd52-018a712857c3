# RAW dataset
RAW dataset includes all data available in ARKitScenes dataset, the 3dod and depth upsampling datasets are subset of it,
RAW dataset includes much more assets that didn't appear in 3dod and depth upsampling datasets that user can use for different tasks.

## Data download
To download the data please follow the [data](../DATA.md) documentation

## Data organization and format of input data

RAW dataset includes variety of assets that sampled in different rates, we can categorize the images assets to 5
categories:
1. `60FPS` - includes 3 low resolution assets that synchronize (no guarantee that all assets exist per timestamp)
   - `lowres_wide` - RGB images of the wide camera (256x192)
   - `lowres_depth` - the depth image acquired by AppleDepth Lidar (256x192)
   - `confidence` - the confidence of the AppleDepth depth image (256x192)
2. `Filtered 10FPS` - includes 2 high resolution assets that synchronize and filtered by high error between `highres_depth` and `lowres_depth`
   - `highres_depth` - the ground-truth depth image projected from the mesh generated by Faro’s laser scanners (1920x1440)
   - `wide` - the RGB images of the wide camera (1920x1440)
3. `10FPS` - include 1 VGA resolution asset from ultra wide camera (different timestamp from the assets in section 2)
   - `ultrawide` - the RGB images of the ultra wide  camera (640x480)
4. `30FPS` - include 1 VGA resolution asset from wide camera
   - `vga_wide` - the RGB images of the wide camera (640x480)
5. `Per venue` - the high resolution laser scanner point clouds which were produced by FARO lidar scanners. There are multiple point clouds for each venue, taken from different locations to increase coverage. Each scan includes 2 assets: RGBD point-cloud of the scan and transformation data used for registering multiple point-clouds. Please see the `has_laser_scanner_point_clouds` column of the metadata csv to check if those point-clouds are available, per video id. 

In addition each RGB image include the intrinsic matrix information in a folder with the same name as the asset name with
the suffix=`_intrinsics` (i.e. `lowres_wide_intrinsics`, `wide_intrinsics`, `ultrawide_intrinsics`, `vga_wide_intrinsics`)

The 3dod dataset is a subset of the category 1 (60FPS),
and depth upsampling dataset is subset of the categories 1 and 2.

important: raw dataset and 3dod dataset created in a different time, this can lead to a slightly different `lowres_depth` and `confidence` assets between raw and 3dod datasets.
