# Configuration for finetuning VGGT using ONLY Dust3R datasets
# This configuration removes all original VGGT datasets and uses only Dust3R data for finetuning

# Finetune configuration using only Dust3R datasets
defaults:
  - default
  - _self_  # This ensures our config overrides default

# Override dataset configuration to use ONLY Dust3R datasets (no original VGGT datasets)
data:
  train:
    _target_: data.dynamic_dataloader.DynamicTorchDataset
    num_workers: ${num_workers}
    max_img_per_gpu: ${max_img_per_gpu}
    common_config:
      img_size: ${img_size}
      patch_size: ${patch_size}
      debug: True
      repeat_batch: False
      seed: 42
      training: True
      load_track: False
      track_num: 256
      fix_img_num: 8  # Number of images per sequence
      fix_aspect_ratio: 1.0
      inside_random: False
      augs:
        scales: [0.8, 1.2]
        cojitter: True
        cojitter_ratio: 0.5
        color_jitter:
          brightness: 0.2
          contrast: 0.2
          saturation: 0.2
          hue: 0.1
          p: 0.8
        gray_scale: 0.1
        gau_blur: 0.1
      rescale: True
      rescale_aug: True
      landscape_check: True
    dataset:
      _target_: data.composed_dataset.ComposedDataset
      dataset_configs:
        # =================================================================
        # DUST3R DATASETS ONLY - FOR FINETUNING
        # =================================================================
        
        # ================================================================= 
        # AVAILABLE DUST3R DATASETS - ONLY ENABLED ONES WILL BE USED
        # =================================================================
        
        # Co3d dataset - NOT AVAILABLE in current data directory
        # - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
        #   dust3r_dataset_config: "Co3d(split='train', ROOT='/defaultShare/pubdata/3D/dust3r/co3d_pairs', resolution=224, aug_crop=16)"
        #   split: train
        #   len_train: 50000
        #   sequence_length: 8
        
        # BlendedMVS dataset ✅ AVAILABLE
        - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
          dust3r_dataset_config: "BlendedMVS(split='train', ROOT='/defaultShare/pubdata/3D/dust3r', resolution=224)"
          split: train  
          len_train: 30000
          sequence_length: 8
        
        # ARKitScenes dataset ✅ AVAILABLE
        - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
          dust3r_dataset_config: "ARKitScenes(split='train', ROOT='/defaultShare/pubdata/3D/dust3r', resolution=224)"
          split: train
          len_train: 40000  
          sequence_length: 8
        
        # ScanNet++ dataset ✅ AVAILABLE
        - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
          dust3r_dataset_config: "ScanNetpp(split='train', ROOT='/defaultShare/pubdata/3D/dust3r', resolution=224)"
          split: train
          len_train: 35000
          sequence_length: 8

        # MegaDepth dataset ✅ AVAILABLE (fixed path with .npz extension)
        - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
          dust3r_dataset_config: "MegaDepth(split='train', ROOT='/defaultShare/pubdata/3D/dust3r', resolution=224)"
          split: train
          len_train: 25000
          sequence_length: 8

        # For Waymo dataset:
        # - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
        #   dust3r_dataset_config: "Waymo(split='train', ROOT='/path/to/waymo', resolution=224)"

        # For Habitat dataset:
        # - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
        #   dust3r_dataset_config: "Habitat(split='train', ROOT='/path/to/habitat', resolution=224)"

        # For StaticThings3D dataset:
        # - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
        #   dust3r_dataset_config: "StaticThings3D(split='train', ROOT='/path/to/staticthings3d', resolution=224)"

        # For WildRGBD dataset:
        # - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
        #   dust3r_dataset_config: "WildRGBD(split='train', ROOT='/path/to/wildrgbd', resolution=224)"

  val:
    _target_: data.dynamic_dataloader.DynamicTorchDataset
    num_workers: ${num_workers}
    max_img_per_gpu: ${max_img_per_gpu}
    common_config:
      img_size: ${img_size}
      patch_size: ${patch_size}
      debug: True
      training: False
      load_track: False
      track_num: 256
      fix_img_num: 8
      fix_aspect_ratio: 1.0
      inside_random: False
      augs:
        scales: [1.0, 1.0]  # No augmentation for validation
        cojitter: False
        cojitter_ratio: 0.0
        color_jitter: null  # No color jitter for validation
        gray_scale: 0.0
        gau_blur: 0.0
      rescale: True
      rescale_aug: False
      landscape_check: True
    dataset:
      _target_: data.composed_dataset.ComposedDataset
      dataset_configs:
        # Use BlendedMVS for validation (since Co3d is not available)
        - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
          dust3r_dataset_config: "BlendedMVS(split='val', ROOT='/defaultShare/pubdata/3D/dust3r', resolution=224)"
          split: val
          len_test: 5000
          sequence_length: 8

# =================================================================
# FINETUNING SPECIFIC OVERRIDES
# =================================================================

# Lower learning rate for finetuning (vs 5e-5 for pretraining)
optim:
  optimizer:
    lr: 2e-5  # Lower learning rate for finetuning

# Ensure we start from pretrained checkpoint
checkpoint:
  resume_checkpoint_path: /defaultShare/pubdata/3D/ckpt/vggt/model.pt
  strict: False  # Allow flexible loading for finetuning

# Reduce training epochs for finetuning  
max_epochs: 20  # Fewer epochs for finetuning vs full training

# You can adjust these training parameters for finetuning:
# max_epochs: 10           # Even fewer epochs for quick finetuning
# val_epoch_freq: 1        # Validate every epoch
# limit_train_batches: 1000 # Limit training batches for quick testing

# =================================================================
     
# Example configurations for different Dust3R datasets:

# For using multiple Dust3R datasets simultaneously:
# data:
#   train:
#     dataset:
#       _target_: data.composed_dataset.ComposedDataset
#       dataset_configs:
#         - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#           dust3r_dataset_config: "Co3d(split='train', ROOT='/path/to/co3d', resolution=224)"
#           len_train: 30000
#         - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter  
#           dust3r_dataset_config: "BlendedMVS(split='train', ROOT='/path/to/blendedmvs', resolution=224)"
#           len_train: 20000
#         - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#           dust3r_dataset_config: "ARKitScenes(split='train', ROOT='/path/to/arkitscenes', resolution=224)"
#           len_train: 25000

# For MegaDepth dataset:
# - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#   dust3r_dataset_config: "MegaDepth(split='train', ROOT='/path/to/megadepth', resolution=224)"

# For Waymo dataset:
# - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#   dust3r_dataset_config: "Waymo(split='train', ROOT='/path/to/waymo', resolution=224)"

# For Habitat dataset:
# - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#   dust3r_dataset_config: "Habitat(split='train', ROOT='/path/to/habitat', resolution=224)"

# For StaticThings3D dataset:
# - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#   dust3r_dataset_config: "StaticThings3D(split='train', ROOT='/path/to/staticthings3d', resolution=224)"

# For WildRGBD dataset:
# - _target_: data.datasets.dust3r_adapter.Dust3RDatasetAdapter
#   dust3r_dataset_config: "WildRGBD(split='train', ROOT='/path/to/wildrgbd', resolution=224)"