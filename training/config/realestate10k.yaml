defaults:
  - default_dataset.yaml

exp_name: realestate10k_exp
img_size: 518
num_workers: 8
seed_value: 42
accum_steps: 1
patch_size: 14
val_epoch_freq: 1
max_img_per_gpu: 32

mode: "val"
limit_train_batches: null
limit_val_batches: 100

data:
  val:
    _target_: data.dynamic_dataloader.DynamicTorchDataset
    num_workers: ${num_workers}
    max_img_per_gpu: ${max_img_per_gpu}
    common_config:
      img_size: ${img_size}
      patch_size: ${patch_size}
      debug: True
    dataset:
      _target_: data.composed_dataset.ComposedDataset
      dataset_configs:
        - _target_: data.datasets.realestate10k.RealEstate10KDataset
          split: test
          data_root: /path/to/realestate10k/data
          min_num_frames: 8
          max_num_frames: 16
          len_test: 10000
          use_depth: True
          use_pose: True

logging:
  log_dir: logs/${exp_name}
  log_visuals: True
  log_freq: 1
  log_level_primary: INFO
  log_level_secondary: WARNING
  all_ranks: False
  tensorboard_writer:
    _target_: train_utils.tb_writer.TensorBoardLogger
    path: ${logging.log_dir}/tensorboard
  scalar_keys_to_log:
    val:
      keys_to_log:
        - loss_objective
        - loss_camera
        - loss_T
        - loss_R
        - loss_FL
        - loss_conf_depth
        - loss_reg_depth
        - loss_grad_depth
  log_visual_frequency:
    val: 10
  visuals_keys_to_log:
    val:
      keys_to_log:
        - images
        - depths
      modality: image

checkpoint:
  save_dir: logs/${exp_name}/ckpts
  save_freq: 0
  resume_checkpoint_path: /path/to/your/trained/model.pt
  strict: False

loss:
  _target_: loss.MultitaskLoss
  camera: 
    weight: 5.0
    loss_type: "l1"
  depth:
    weight: 1.0
    gradient_loss_fn: "grad" 
    valid_range: 0.98
  point: null
  track: null

optim:
  param_group_modifiers: False
  optimizer:
    _target_: torch.optim.AdamW
    lr: 1e-5
    weight_decay: 0.05
  amp:
    enabled: True
    amp_dtype: bfloat16
  gradient_clip:
    _target_: train_utils.gradient_clip.GradientClipper
    configs:
      - module_name: ["aggregator"]
        max_norm: 1.0
        norm_type: 2
      - module_name: ["depth"]
        max_norm: 1.0
        norm_type: 2
      - module_name: ["camera"]
        max_norm: 1.0
        norm_type: 2

max_epochs: 1

model:
  _target_: vggt.models.vggt.VGGT
  enable_camera: True
  enable_depth: True
  enable_point: False
  enable_track: False

distributed:
  backend: nccl
  comms_dtype: None
  find_unused_parameters: False
  timeout_mins: 30
  gradient_as_bucket_view: True
  bucket_cap_mb: 25
  broadcast_buffers: True

cuda:
  cudnn_deterministic: False
  cudnn_benchmark: False
  allow_tf32: True 