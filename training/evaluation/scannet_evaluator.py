#!/usr/bin/env python3
# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
ScanNet Evaluation Module for VGGT

Implements evaluation metrics for camera pose estimation, depth prediction,
and 3D point cloud reconstruction quality on ScanNet dataset.

This improved version ensures strict compliance with VGGT paper evaluation protocol.
"""

import os
import sys
from pathlib import Path
# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

import numpy as np
import torch
import torch.nn.functional as F
import cv2
import logging
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import json

from vggt.utils.pose_enc import pose_encoding_to_extri_intri
from vggt.utils.geometry import unproject_depth_map_to_point_map
from training.visualization.scannet_visualizer import ScanNetVisualizer


class ScanNetEvaluator:
    """
    Core evaluation class for ScanNet metrics following VGGT paper protocol.
    
    Computes standard 3D vision metrics including:
    - Camera pose accuracy (translation/rotation errors)
    - Depth estimation quality (absolute/relative errors, accuracy thresholds)
    - Point cloud reconstruction metrics (Chamfer distance, F-score)
    """
    
    def __init__(self, 
                 save_predictions: bool = True,
                 save_visualizations: bool = True,
                 output_dir: str = "scannet_results"):
        """
        Initialize the ScanNet evaluator.
        
        Args:
            save_predictions: Whether to save prediction results
            save_visualizations: Whether to generate visualizations
            output_dir: Directory to save outputs
        """
        self.save_predictions = save_predictions
        self.save_visualizations = save_visualizations
        self.output_dir = Path(output_dir)
        
        # Create output directories
        self.predictions_dir = self.output_dir / "predictions"
        self.visualizations_dir = self.output_dir / "visualizations"
        
        if save_predictions:
            self.predictions_dir.mkdir(parents=True, exist_ok=True)
        if save_visualizations:
            self.visualizations_dir.mkdir(parents=True, exist_ok=True)
            # Initialize visualizer
            self.visualizer = ScanNetVisualizer(str(self.visualizations_dir))
        else:
            self.visualizer = None
        
        # Initialize metrics storage
        self.reset_metrics()
        
        logging.info(f"ScanNet evaluator initialized with output dir: {output_dir}")
    
    def reset_metrics(self):
        """Reset all accumulated metrics."""
        self.pose_errors = {
            'translation_errors': [],
            'rotation_errors': [],
            'successful_poses_strict': 0,
            'successful_poses_standard': 0,
            'successful_poses_loose': 0,
            'total_poses': 0
        }
        
        self.depth_errors = {
            'abs_rel': [],
            'sq_rel': [],
            'rmse': [],
            'rmse_log': [],
            'accuracy_1_25': [],
            'accuracy_1_25_2': [],
            'accuracy_1_25_3': []
        }
        
        self.point_cloud_errors = {
            'chamfer_distances': [],
            'f_scores_01': [],
            'f_scores_02': [],
            'completeness': [],
            'accuracy': []
        }
        
        self.scene_results = {}
    
    def evaluate_batch(self, 
                      predictions: Dict, 
                      batch: Dict, 
                      scene_name: str) -> Dict:
        """
        Evaluate a single batch of predictions.
        
        Args:
            predictions: Model predictions dict
            batch: Ground truth batch dict
            scene_name: Name of the scene being evaluated
            
        Returns:
            Dict with batch-level metrics
        """
        # Safely get frame_num with fallback
        if 'frame_num' in batch:
            frame_count = batch['frame_num']
        elif 'images' in batch and hasattr(batch['images'], '__len__'):
            frame_count = len(batch['images'])
        else:
            logging.warning(f"Cannot determine frame count for scene {scene_name}, defaulting to 1")
            frame_count = 1
            
        batch_results = {
            'scene_name': scene_name,
            'frame_count': frame_count,
            'pose_metrics': {},
            'depth_metrics': {},
            'point_cloud_metrics': {}
        }
        
        # Convert batch data to tensors if needed
        batch = self._prepare_batch_tensors(batch)
        
        # Extract ground truth data with proper error handling
        try:
            gt_extrinsics = batch['extrinsics']  # (B, N, 4, 4)
            gt_intrinsics = batch['intrinsics']  # (B, N, 3, 3)
            gt_depths = batch['depths']          # (B, N, H, W)
            gt_world_points = batch['world_points']  # (B, N, H, W, 3)
            point_masks = batch['point_masks']   # (B, N, H, W)
        except KeyError as e:
            logging.error(f"Missing required ground truth data for scene {scene_name}: {e}")
            return batch_results
        
        # Evaluate camera poses if predicted
        if 'pose_enc_list' in predictions:
            pose_metrics = self._evaluate_camera_poses(
                predictions['pose_enc_list'][-1],  # Use final stage prediction
                gt_extrinsics, 
                gt_intrinsics,
                point_masks,
                batch  # Pass batch for image size info
            )
            batch_results['pose_metrics'] = pose_metrics
            self._update_pose_metrics(pose_metrics)
        
        # Evaluate depth predictions if available
        if 'depth' in predictions:
            depth_metrics = self._evaluate_depth_predictions(
                predictions['depth'],
                gt_depths,
                point_masks
            )
            batch_results['depth_metrics'] = depth_metrics
            self._update_depth_metrics(depth_metrics)
        
        # Evaluate 3D point cloud if available
        if 'world_points' in predictions:
            pc_metrics = self._evaluate_point_clouds(
                predictions['world_points'],
                gt_world_points,
                point_masks
            )
            batch_results['point_cloud_metrics'] = pc_metrics
            self._update_point_cloud_metrics(pc_metrics)
        
        # Save predictions if requested
        if self.save_predictions:
            self._save_batch_predictions(predictions, batch, scene_name)
        
        # Generate visualizations if requested
        if self.save_visualizations and self.visualizer is not None:
            self._generate_batch_visualizations(predictions, batch, scene_name)
        
        # Store scene results
        self.scene_results[scene_name] = batch_results
        
        return batch_results
    
    def _prepare_batch_tensors(self, batch: Dict) -> Dict:
        """
        Convert batch data to proper tensor format.
        
        Args:
            batch: Input batch dictionary
            
        Returns:
            Dict: Batch with properly formatted tensors
        """
        converted_batch = {}
        
        for key, value in batch.items():
            if key in ['extrinsics', 'intrinsics', 'depths', 'world_points', 'point_masks']:
                if isinstance(value, np.ndarray):
                    converted_batch[key] = torch.from_numpy(value).float()
                elif isinstance(value, list):
                    # Convert list to tensor
                    if len(value) > 0 and isinstance(value[0], np.ndarray):
                        converted_batch[key] = torch.from_numpy(np.stack(value)).float()
                    else:
                        converted_batch[key] = torch.tensor(value).float()
                elif isinstance(value, torch.Tensor):
                    converted_batch[key] = value.float()
                else:
                    converted_batch[key] = value
            else:
                converted_batch[key] = value
        
        # Ensure proper batch dimensions
        for key in ['extrinsics', 'intrinsics', 'depths', 'world_points', 'point_masks']:
            if key in converted_batch:
                tensor = converted_batch[key]
                if len(tensor.shape) == 3 and key in ['extrinsics', 'intrinsics']:
                    # Add batch dimension for (N, 4, 4) -> (1, N, 4, 4)
                    converted_batch[key] = tensor.unsqueeze(0)
                elif len(tensor.shape) == 2 and key in ['depths', 'point_masks']:
                    # Add batch and sequence dimensions for (H, W) -> (1, 1, H, W)
                    converted_batch[key] = tensor.unsqueeze(0).unsqueeze(0)
                elif len(tensor.shape) == 3 and key in ['depths', 'point_masks']:
                    # Add batch dimension for (N, H, W) -> (1, N, H, W)
                    converted_batch[key] = tensor.unsqueeze(0)
                elif len(tensor.shape) == 4 and key == 'world_points':
                    # Add batch dimension for (N, H, W, 3) -> (1, N, H, W, 3)
                    converted_batch[key] = tensor.unsqueeze(0)
        
        return converted_batch
    
    def _evaluate_camera_poses(self, 
                              pred_pose_encodings: torch.Tensor,
                              gt_extrinsics: torch.Tensor,
                              gt_intrinsics: torch.Tensor,
                              point_masks: torch.Tensor,
                              batch: Dict) -> Dict:
        """
        Evaluate camera pose estimation accuracy following VGGT paper protocol.
        
        Args:
            pred_pose_encodings: Predicted pose encodings (B, N, encoding_dim)
            gt_extrinsics: Ground truth extrinsic matrices (B, N, 4, 4)
            gt_intrinsics: Ground truth intrinsic matrices (B, N, 3, 3)
            point_masks: Valid point masks (B, N, H, W)
            batch: Batch data for context
            
        Returns:
            Dict with pose evaluation metrics
        """
        B, N = pred_pose_encodings.shape[:2]
        translation_errors = []
        rotation_errors = []
        successful_poses_strict = 0  # <0.25m & <2°
        successful_poses_standard = 0  # <0.5m & <5°
        successful_poses_loose = 0  # <1.0m & <10°
        
        # Get image size from batch - improved size detection
        H, W = self._get_image_size_from_batch(batch)
            
        # Convert predictions to extrinsic/intrinsic matrices
        try:
            pred_extrinsics, pred_intrinsics = pose_encoding_to_extri_intri(
                pred_pose_encodings,
                image_size_hw=(H, W),
                pose_encoding_type="absT_quaR_FoV"
            )
        except Exception as e:
            logging.error(f"Failed to decode pose encodings: {e}")
            return {
                'translation_errors': [],
                'rotation_errors': [],
                'translation_error_mean': 0.0,
                'translation_error_std': 0.0,
                'rotation_error_mean': 0.0,
                'rotation_error_std': 0.0,
                'success_rate': 0.0,
                'successful_poses': 0,
                'total_poses': 0
            }
        
        for b in range(B):
            for n in range(N):
                # Check if frame has sufficient valid points
                valid_points = point_masks[b, n].sum()
                if valid_points < 100:  # Minimum threshold for reliable evaluation
                    continue
                
                # Extract poses (camera-to-world)
                pred_pose = pred_extrinsics[b, n].detach().cpu().numpy()
                gt_pose = gt_extrinsics[b, n].detach().cpu().numpy()
                
                # Compute translation error (in meters) - VGGT paper uses Euclidean distance
                pred_t = pred_pose[:3, 3]
                gt_t = gt_pose[:3, 3]
                trans_error = np.linalg.norm(pred_t - gt_t)
                
                # Compute rotation error (in degrees) - VGGT paper protocol
                pred_R = pred_pose[:3, :3]
                gt_R = gt_pose[:3, :3]
                
                # Relative rotation matrix
                R_rel = pred_R @ gt_R.T
                
                # Convert to angle-axis representation with numerical stability
                trace = np.trace(R_rel)
                trace = np.clip(trace, -1.0, 3.0)  # Numerical stability
                angle = np.arccos(np.clip((trace - 1) / 2, -1.0, 1.0))
                rot_error = np.degrees(angle)
                
                translation_errors.append(trans_error)
                rotation_errors.append(rot_error)
                
                # VGGT paper success criteria: multiple thresholds for comprehensive evaluation
                # Strict: <0.25m & <2° (high precision)
                if trans_error < 0.25 and rot_error < 2.0:
                    successful_poses_strict += 1
                
                # Standard: <0.5m & <5° (commonly used benchmark)
                if trans_error < 0.5 and rot_error < 5.0:
                    successful_poses_standard += 1
                
                # Loose: <1.0m & <10° (general feasibility)
                if trans_error < 1.0 and rot_error < 10.0:
                    successful_poses_loose += 1
        
        total_poses = len(translation_errors)
        
        return {
            'translation_errors': translation_errors,
            'rotation_errors': rotation_errors,
            'translation_error_mean': np.mean(translation_errors) if translation_errors else 0.0,
            'translation_error_std': np.std(translation_errors) if translation_errors else 0.0,
            'translation_error_median': np.median(translation_errors) if translation_errors else 0.0,
            'rotation_error_mean': np.mean(rotation_errors) if rotation_errors else 0.0,
            'rotation_error_std': np.std(rotation_errors) if rotation_errors else 0.0,
            'rotation_error_median': np.median(rotation_errors) if rotation_errors else 0.0,
            'success_rate_strict': successful_poses_strict / max(total_poses, 1),
            'success_rate_standard': successful_poses_standard / max(total_poses, 1),
            'success_rate_loose': successful_poses_loose / max(total_poses, 1),
            'successful_poses_strict': successful_poses_strict,
            'successful_poses_standard': successful_poses_standard,
            'successful_poses_loose': successful_poses_loose,
            'total_poses': total_poses
        }
    
    def _get_image_size_from_batch(self, batch: Dict) -> Tuple[int, int]:
        """
        Extract image size from batch data with multiple fallback methods.
        
        Args:
            batch: Batch dictionary
            
        Returns:
            Tuple[int, int]: (Height, Width)
        """
        # Method 1: From images tensor
        if 'images' in batch:
            images = batch['images']
            if isinstance(images, torch.Tensor):
                if len(images.shape) == 5:  # (B, N, C, H, W)
                    return images.shape[3], images.shape[4]
                elif len(images.shape) == 4:  # (N, C, H, W)
                    return images.shape[2], images.shape[3]
            elif isinstance(images, list) and len(images) > 0:
                if isinstance(images[0], torch.Tensor):
                    return images[0].shape[-2], images[0].shape[-1]
        
        # Method 2: From depths tensor
        if 'depths' in batch:
            depths = batch['depths']
            if isinstance(depths, torch.Tensor) and len(depths.shape) >= 2:
                return depths.shape[-2], depths.shape[-1]
        
        # Method 3: From point_masks
        if 'point_masks' in batch:
            masks = batch['point_masks']
            if isinstance(masks, torch.Tensor) and len(masks.shape) >= 2:
                return masks.shape[-2], masks.shape[-1]
        
        # Method 4: Default ScanNet image size
        logging.warning("Could not determine image size from batch, using default ScanNet size (480, 640)")
        return 480, 640
    
    def _evaluate_depth_predictions(self,
                                   pred_depths: torch.Tensor,
                                   gt_depths: torch.Tensor,
                                   point_masks: torch.Tensor) -> Dict:
        """
        Evaluate depth prediction accuracy following VGGT paper metrics.
        
        Args:
            pred_depths: Predicted depth maps (B, N, H, W) or (B, N, C, H, W)
            gt_depths: Ground truth depth maps (B, N, H, W)
            point_masks: Valid point masks (B, N, H, W)
            
        Returns:
            Dict with depth evaluation metrics
        """
        # Convert to numpy with proper handling
        if isinstance(pred_depths, torch.Tensor):
            pred_depths = pred_depths.detach().cpu().numpy()
        if isinstance(gt_depths, torch.Tensor):
            gt_depths = gt_depths.detach().cpu().numpy()
        if isinstance(point_masks, torch.Tensor):
            point_masks = point_masks.detach().cpu().numpy().astype(bool)
        
        # Initialize metric lists
        abs_rel_errors = []
        sq_rel_errors = []
        rmse_errors = []
        rmse_log_errors = []
        accuracy_1_25 = []
        accuracy_1_25_2 = []
        accuracy_1_25_3 = []
        
        # Handle different depth tensor shapes with improved robustness
        if len(pred_depths.shape) == 5:  # (B, N, C, H, W) - handle channel dimension
            if pred_depths.shape[2] == 1:  # Single channel, squeeze it
                pred_depths = pred_depths.squeeze(2)
            else:  # Multiple channels, take first channel
                pred_depths = pred_depths[:, :, 0, :, :]
        elif len(pred_depths.shape) == 3:  # (B, H, W) - add sequence dimension
            pred_depths = np.expand_dims(pred_depths, axis=1)
        elif len(pred_depths.shape) == 2:  # (H, W) - add batch and sequence dimensions
            pred_depths = np.expand_dims(np.expand_dims(pred_depths, axis=0), axis=0)
        
        # Ensure gt_depths has proper shape
        if len(gt_depths.shape) == 3:  # (N, H, W) - add batch dimension
            gt_depths = np.expand_dims(gt_depths, axis=0)
        elif len(gt_depths.shape) == 2:  # (H, W) - add batch and sequence dimensions
            gt_depths = np.expand_dims(np.expand_dims(gt_depths, axis=0), axis=0)
        
        # Ensure point_masks has proper shape
        if len(point_masks.shape) == 3:  # (N, H, W) - add batch dimension
            point_masks = np.expand_dims(point_masks, axis=0)
        elif len(point_masks.shape) == 2:  # (H, W) - add batch and sequence dimensions
            point_masks = np.expand_dims(np.expand_dims(point_masks, axis=0), axis=0)
        
        B, N, H, W = pred_depths.shape
        
        for b in range(B):
            for n in range(N):
                pred = pred_depths[b, n]
                gt = gt_depths[b, n]
                mask = point_masks[b, n]
                
                if not np.any(mask):
                    continue
                
                # Ensure pred and mask have compatible shapes
                if pred.shape != mask.shape:
                    # Resize pred to match mask shape if needed
                    if len(pred.shape) == 2 and len(mask.shape) == 2:
                        # Convert to torch tensor if it's numpy
                        if isinstance(pred, np.ndarray):
                            pred_tensor = torch.from_numpy(pred).float()
                        else:
                            pred_tensor = pred.float()
                        
                        pred_tensor = F.interpolate(pred_tensor.unsqueeze(0).unsqueeze(0), 
                                                   size=mask.shape, 
                                                   mode='bilinear', 
                                                   align_corners=False).squeeze(0).squeeze(0)
                        pred = pred_tensor.cpu().numpy()
                    else:
                        # Skip this frame if shapes are incompatible
                        continue
                
                # Apply mask and remove invalid depths
                pred_valid = pred[mask]
                gt_valid = gt[mask]
                
                # Remove zero/invalid depths
                valid_depth_mask = (gt_valid > 0.1) & (gt_valid < 10.0) & (pred_valid > 0.1)
                if not np.any(valid_depth_mask):
                    continue
                
                pred_valid = pred_valid[valid_depth_mask]
                gt_valid = gt_valid[valid_depth_mask]
                
                # Compute depth metrics
                abs_rel = np.mean(np.abs(pred_valid - gt_valid) / gt_valid)
                sq_rel = np.mean(((pred_valid - gt_valid) ** 2) / gt_valid)
                rmse = np.sqrt(np.mean((pred_valid - gt_valid) ** 2))
                rmse_log = np.sqrt(np.mean((np.log(pred_valid) - np.log(gt_valid)) ** 2))
                
                # Accuracy thresholds
                threshold = np.maximum(pred_valid / gt_valid, gt_valid / pred_valid)
                acc_1_25 = np.mean(threshold < 1.25)
                acc_1_25_2 = np.mean(threshold < 1.25**2)
                acc_1_25_3 = np.mean(threshold < 1.25**3)
                
                # Store metrics
                abs_rel_errors.append(abs_rel)
                sq_rel_errors.append(sq_rel)
                rmse_errors.append(rmse)
                rmse_log_errors.append(rmse_log)
                accuracy_1_25.append(acc_1_25)
                accuracy_1_25_2.append(acc_1_25_2)
                accuracy_1_25_3.append(acc_1_25_3)
        
        return {
            'abs_rel_error': np.mean(abs_rel_errors) if abs_rel_errors else 0.0,
            'sq_rel_error': np.mean(sq_rel_errors) if sq_rel_errors else 0.0,
            'rmse': np.mean(rmse_errors) if rmse_errors else 0.0,
            'rmse_log': np.mean(rmse_log_errors) if rmse_log_errors else 0.0,
            'accuracy_1.25': np.mean(accuracy_1_25) if accuracy_1_25 else 0.0,
            'accuracy_1.25^2': np.mean(accuracy_1_25_2) if accuracy_1_25_2 else 0.0,
            'accuracy_1.25^3': np.mean(accuracy_1_25_3) if accuracy_1_25_3 else 0.0,
            'num_valid_pixels': len(abs_rel_errors)
        }
    
    def _evaluate_point_clouds(self,
                              pred_world_points: torch.Tensor,
                              gt_world_points: torch.Tensor,
                              point_masks: torch.Tensor) -> Dict:
        """
        Evaluate 3D point cloud reconstruction quality.
        
        Args:
            pred_world_points: Predicted 3D points (B, N, H, W, 3)
            gt_world_points: Ground truth 3D points (B, N, H, W, 3)
            point_masks: Valid point masks (B, N, H, W)
            
        Returns:
            Dict with point cloud evaluation metrics
        """
        pred_points = pred_world_points.detach().cpu().numpy()
        gt_points = gt_world_points.detach().cpu().numpy()
        point_masks = point_masks.detach().cpu().numpy().astype(bool)
        
        chamfer_distances = []
        f_scores_01 = []
        f_scores_02 = []
        completeness_scores = []
        accuracy_scores = []
        
        B, N, H, W, _ = pred_points.shape
        
        for b in range(B):
            for n in range(N):
                pred = pred_points[b, n]
                gt = gt_points[b, n]
                mask = point_masks[b, n]
                
                if not np.any(mask):
                    continue
                
                # Extract valid points
                pred_pc = pred[mask].reshape(-1, 3)
                gt_pc = gt[mask].reshape(-1, 3)
                
                # Remove invalid points (zeros or extreme values)
                pred_valid_mask = np.all(np.abs(pred_pc) < 50, axis=1)  # Within 50m
                gt_valid_mask = np.all(np.abs(gt_pc) < 50, axis=1)
                
                if not (np.any(pred_valid_mask) and np.any(gt_valid_mask)):
                    continue
                
                pred_pc = pred_pc[pred_valid_mask]
                gt_pc = gt_pc[gt_valid_mask]
                
                # Compute point cloud metrics
                metrics = self._compute_point_cloud_metrics(pred_pc, gt_pc)
                
                chamfer_distances.append(metrics['chamfer_distance'])
                f_scores_01.append(metrics['f_score_0.01'])
                f_scores_02.append(metrics['f_score_0.02'])
                completeness_scores.append(metrics['completeness'])
                accuracy_scores.append(metrics['accuracy'])
        
        return {
            'chamfer_distance': np.mean(chamfer_distances) if chamfer_distances else 0.0,
            'f_score_0.01': np.mean(f_scores_01) if f_scores_01 else 0.0,
            'f_score_0.02': np.mean(f_scores_02) if f_scores_02 else 0.0,
            'completeness': np.mean(completeness_scores) if completeness_scores else 0.0,
            'accuracy': np.mean(accuracy_scores) if accuracy_scores else 0.0,
            'num_valid_frames': len(chamfer_distances)
        }
    
    def _compute_point_cloud_metrics(self, pred_pc: np.ndarray, gt_pc: np.ndarray) -> Dict:
        """
        Compute point cloud comparison metrics.
        
        Args:
            pred_pc: Predicted point cloud (N, 3)
            gt_pc: Ground truth point cloud (M, 3)
            
        Returns:
            Dict with computed metrics
        """
        # Compute pairwise distances
        pred_expanded = pred_pc[:, np.newaxis, :]  # (N, 1, 3)
        gt_expanded = gt_pc[np.newaxis, :, :]      # (1, M, 3)
        
        distances = np.linalg.norm(pred_expanded - gt_expanded, axis=2)  # (N, M)
        
        # Chamfer distance (bidirectional)
        pred_to_gt_min = np.min(distances, axis=1)  # (N,)
        gt_to_pred_min = np.min(distances, axis=0)  # (M,)
        
        chamfer_distance = np.mean(pred_to_gt_min) + np.mean(gt_to_pred_min)
        
        # Precision and recall at different thresholds
        def compute_precision_recall(threshold):
            # Precision: fraction of predicted points within threshold of GT
            precision = np.mean(pred_to_gt_min < threshold)
            # Recall: fraction of GT points within threshold of predictions
            recall = np.mean(gt_to_pred_min < threshold)
            return precision, recall
        
        # F-scores at 1cm and 2cm thresholds
        precision_01, recall_01 = compute_precision_recall(0.01)
        precision_02, recall_02 = compute_precision_recall(0.02)
        
        f_score_01 = 2 * precision_01 * recall_01 / (precision_01 + recall_01 + 1e-8)
        f_score_02 = 2 * precision_02 * recall_02 / (precision_02 + recall_02 + 1e-8)
        
        return {
            'chamfer_distance': chamfer_distance,
            'f_score_0.01': f_score_01,
            'f_score_0.02': f_score_02,
            'completeness': recall_01,  # Completeness at 1cm
            'accuracy': precision_01,   # Accuracy at 1cm
        }
    
    def _update_pose_metrics(self, metrics: Dict):
        """Update accumulated pose metrics."""
        if 'translation_errors' in metrics:
            self.pose_errors['translation_errors'].extend(metrics['translation_errors'])
        if 'rotation_errors' in metrics:
            self.pose_errors['rotation_errors'].extend(metrics['rotation_errors'])
        self.pose_errors['successful_poses_strict'] += metrics.get('successful_poses_strict', 0)
        self.pose_errors['successful_poses_standard'] += metrics.get('successful_poses_standard', 0)
        self.pose_errors['successful_poses_loose'] += metrics.get('successful_poses_loose', 0)
        self.pose_errors['total_poses'] += metrics.get('total_poses', 0)
    
    def _update_depth_metrics(self, metrics: Dict):
        """Update accumulated depth metrics."""
        # Map returned keys to stored keys
        key_mapping = {
            'abs_rel_error': 'abs_rel',
            'sq_rel_error': 'sq_rel',
            'rmse': 'rmse',
            'rmse_log': 'rmse_log',
            'accuracy_1.25': 'accuracy_1_25',
            'accuracy_1.25^2': 'accuracy_1_25_2',
            'accuracy_1.25^3': 'accuracy_1_25_3'
        }
        
        for returned_key, stored_key in key_mapping.items():
            if returned_key in metrics:
                self.depth_errors[stored_key].append(metrics[returned_key])
    
    def _update_point_cloud_metrics(self, metrics: Dict):
        """Update accumulated point cloud metrics."""
        metric_mapping = {
            'chamfer_distance': 'chamfer_distances',
            'f_score_0.01': 'f_scores_01',
            'f_score_0.02': 'f_scores_02',
            'completeness': 'completeness',
            'accuracy': 'accuracy'
        }
        
        for key, storage_key in metric_mapping.items():
            if key in metrics:
                self.point_cloud_errors[storage_key].append(metrics[key])
    
    def compute_final_metrics(self) -> Dict:
        """
        Compute final aggregated metrics across all scenes.
        
        Returns:
            Dict with final evaluation metrics
        """
        final_metrics = {}
        
        # Camera pose metrics
        if self.pose_errors['translation_errors']:
            final_metrics['camera_pose'] = {
                'translation_error_mean': float(np.mean(self.pose_errors['translation_errors'])),
                'translation_error_std': float(np.std(self.pose_errors['translation_errors'])),
                'translation_error_median': float(np.median(self.pose_errors['translation_errors'])),
                'rotation_error_mean': float(np.mean(self.pose_errors['rotation_errors'])),
                'rotation_error_std': float(np.std(self.pose_errors['rotation_errors'])),
                'rotation_error_median': float(np.median(self.pose_errors['rotation_errors'])),
                'success_rate_strict': self.pose_errors['successful_poses_strict'] / max(self.pose_errors['total_poses'], 1),
                'success_rate_standard': self.pose_errors['successful_poses_standard'] / max(self.pose_errors['total_poses'], 1),
                'success_rate_loose': self.pose_errors['successful_poses_loose'] / max(self.pose_errors['total_poses'], 1),
                'total_valid_poses': self.pose_errors['total_poses']
            }
        
        # Depth metrics
        depth_keys = ['abs_rel', 'sq_rel', 'rmse', 'rmse_log', 
                     'accuracy_1_25', 'accuracy_1_25_2', 'accuracy_1_25_3']
        if any(self.depth_errors[key.replace('.', '_')] for key in depth_keys):
            final_metrics['depth'] = {}
            for key in depth_keys:
                storage_key = key.replace('.', '_')
                if self.depth_errors[storage_key]:
                    final_metrics['depth'][key] = float(np.mean(self.depth_errors[storage_key]))
        
        # Point cloud metrics
        pc_keys = ['chamfer_distances', 'f_scores_01', 'f_scores_02', 'completeness', 'accuracy']
        if any(self.point_cloud_errors[key] for key in pc_keys):
            final_metrics['point_cloud'] = {}
            name_mapping = {
                'chamfer_distances': 'chamfer_distance',
                'f_scores_01': 'f_score_0.01',
                'f_scores_02': 'f_score_0.02',
                'completeness': 'completeness',
                'accuracy': 'accuracy'
            }
            
            for key, name in name_mapping.items():
                if self.point_cloud_errors[key]:
                    final_metrics['point_cloud'][name] = float(np.mean(self.point_cloud_errors[key]))
        
        return final_metrics
    
    def _save_batch_predictions(self, predictions: Dict, batch: Dict, scene_name: str):
        """Save batch predictions to disk."""
        scene_dir = self.predictions_dir / scene_name
        scene_dir.mkdir(exist_ok=True)
        
        # Save prediction data as numpy arrays
        for key, value in predictions.items():
            if isinstance(value, torch.Tensor):
                np.save(scene_dir / f"{key}.npy", value.detach().cpu().numpy())
            elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], torch.Tensor):
                # Handle lists of tensors (like pose_enc_list)
                for i, tensor in enumerate(value):
                    np.save(scene_dir / f"{key}_stage_{i}.npy", tensor.detach().cpu().numpy())
    
    def _generate_batch_visualizations(self, predictions: Dict, batch: Dict, scene_name: str):
        """Generate visualization images for the batch."""
        if self.visualizer is not None:
            self.visualizer.visualize_batch_results(predictions, batch, scene_name)
    
    def save_results(self, output_file: str):
        """
        Save all evaluation results to a JSON file.
        
        Args:
            output_file: Path to output JSON file
        """
        results = {
            'final_metrics': self.compute_final_metrics(),
            'scene_results': self.scene_results,
            'evaluation_summary': {
                'total_scenes': len(self.scene_results),
                'total_poses_evaluated': self.pose_errors['total_poses'],
                'successful_poses_strict': self.pose_errors['successful_poses_strict'],
                'successful_poses_standard': self.pose_errors['successful_poses_standard'],
                'successful_poses_loose': self.pose_errors['successful_poses_loose']
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        # Generate HTML report if visualizer is available
        if self.visualizer is not None:
            self.visualizer.generate_summary_report(results, self.scene_results)
        
        logging.info(f"Evaluation results saved to {output_file}")