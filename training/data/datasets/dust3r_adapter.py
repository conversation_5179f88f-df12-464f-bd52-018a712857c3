# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
Dust3R Dataset Adapter for VGGT

This module provides an adapter that enables VGGT to use Dust3R datasets
for training. It converts Dust3R's output format to VGGT's expected input format.
"""

import logging
import numpy as np
import random
import sys
import os
from typing import Dict, List, Any, Optional

# Add dust3r to path
dust3r_path = os.path.join(os.path.dirname(__file__), '../../../../dust3r')
if dust3r_path not in sys.path:
    sys.path.insert(0, dust3r_path)

from dust3r.datasets import *
from dust3r.utils.geometry import depthmap_to_absolute_camera_coordinates
from data.base_dataset import BaseDataset
from data.dataset_util import depth_to_world_coords_points


class Dust3RDatasetAdapter(BaseDataset):
    """
    Adapter class that wraps Dust3R datasets to work with VGGT training pipeline.
    
    This adapter:
    1. Instantiates a Dust3R dataset using eval() 
    2. Converts Dust3R's paired view format to VGGT's sequence format
    3. Transforms coordinate systems and data types as needed
    """
    
    def __init__(
        self,
        common_conf,
        dust3r_dataset_config: str,
        split: str = "train",
        len_train: int = 100000,
        len_test: int = 10000,
        sequence_length: int = 8,
        **kwargs
    ):
        """
        Initialize the Dust3R dataset adapter.
        
        Args:
            common_conf: Common configuration for VGGT datasets
            dust3r_dataset_config: String configuration for Dust3R dataset (e.g., "Co3d(split='train', ROOT='/path/to/data')")
            split: Dataset split ('train' or 'test')
            len_train: Length for training dataset
            len_test: Length for test dataset  
            sequence_length: Number of images per sequence to generate
            **kwargs: Additional arguments
        """
        super().__init__(common_conf)
        
        self.dust3r_dataset_config = dust3r_dataset_config
        self.split = split
        self.len_train = len_train if split == 'train' else len_test
        self.sequence_length = sequence_length
        
        # Initialize the underlying Dust3R dataset
        try:
            self.dust3r_dataset = eval(dust3r_dataset_config)
            logging.info(f"Successfully initialized Dust3R dataset: {dust3r_dataset_config}")
        except Exception as e:
            logging.error(f"Failed to initialize Dust3R dataset: {e}")
            raise
            
        self.dust3r_length = len(self.dust3r_dataset)
        
        # Set random seed for reproducibility
        self.seed = getattr(common_conf, 'seed', 42)
        random.seed(self.seed)
        np.random.seed(self.seed)
        
        logging.info(f"Dust3R Adapter initialized: {self.dust3r_length} pairs, generating sequences of {sequence_length} images")

    def __len__(self):
        """Return the dataset length for training/validation"""
        return self.len_train

    def _dust3r_view_to_vggt_format(self, views: List[Dict]) -> Dict:
        """
        Convert Dust3R view format to VGGT batch format.
        
        Args:
            views: List of Dust3R view dictionaries
            
        Returns:
            Dict in VGGT format with batch data
        """
        images = []
        depths = []
        extrinsics = []
        intrinsics = []
        cam_points = []
        world_points = []
        point_masks = []
        original_sizes = []
        
        for i, view in enumerate(views):
            # Extract data from Dust3R view
            img = view['img']  # RGB image as numpy array
            depthmap = view['depthmap']  # Depth map as numpy array  
            camera_pose = view['camera_pose']  # 4x4 camera pose matrix
            camera_intrinsics = view['camera_intrinsics']  # 3x3 intrinsics matrix
            
            # Convert image to uint8 if needed
            if img.dtype == np.float32 and img.max() <= 1.0:
                img = (img * 255).astype(np.uint8)
            elif img.dtype != np.uint8:
                img = img.astype(np.uint8)
                
            # Ensure depth is float32
            if depthmap.dtype != np.float32:
                depthmap = depthmap.astype(np.float32)
                
            # Store original size
            original_size = np.array(img.shape[:2])
            
            # Convert camera pose to extrinsic matrix (camera-to-world -> world-to-camera)
            # Dust3R uses camera-to-world, VGGT expects world-to-camera
            extrinsic = np.linalg.inv(camera_pose).astype(np.float32)
            
            # Ensure intrinsics is 3x3 float32
            intrinsic = camera_intrinsics.astype(np.float32)
            if intrinsic.shape != (3, 3):
                # Handle case where intrinsics might be in different format
                if intrinsic.shape == (3,):
                    # [fx, fy, cx, cy] format
                    fx, fy, cx = intrinsic[0], intrinsic[1], intrinsic[2] if len(intrinsic) > 2 else img.shape[1]/2
                    cy = intrinsic[3] if len(intrinsic) > 3 else img.shape[0]/2
                    intrinsic = np.array([[fx, 0, cx], [0, fy, cy], [0, 0, 1]], dtype=np.float32)
            
            # Convert depth to world and camera coordinates  
            world_coords_points, cam_coords_points, point_mask = (
                depth_to_world_coords_points(depthmap, extrinsic, intrinsic)
            )
            
            # Append to lists
            images.append(img)
            depths.append(depthmap)
            extrinsics.append(extrinsic)
            intrinsics.append(intrinsic)
            cam_points.append(cam_coords_points)
            world_points.append(world_coords_points)
            point_masks.append(point_mask)
            original_sizes.append(original_size)
        
        # Create sequence name
        seq_name = f"dust3r_{views[0]['dataset']}_{views[0]['label']}_{random.randint(0, 999999)}"
        ids = np.arange(len(views))
        
        batch = {
            "seq_name": seq_name,
            "ids": ids,
            "frame_num": len(views),
            "images": images,
            "depths": depths,
            "extrinsics": extrinsics,
            "intrinsics": intrinsics,
            "cam_points": cam_points,
            "world_points": world_points,
            "point_masks": point_masks,
            "original_sizes": original_sizes,
            "tracks": None,  # Dust3R doesn't provide tracks
            "track_masks": None,
        }
        
        return batch

    def _generate_sequence_from_pairs(self, target_length: int) -> List[Dict]:
        """
        Generate a sequence of views by sampling multiple Dust3R pairs.
        
        Args:
            target_length: Desired number of views in sequence
            
        Returns:
            List of Dust3R views forming a sequence
        """
        views = []
        used_indices = set()
        
        # Sample pairs to build up a sequence
        pairs_needed = max(1, target_length // 2)
        
        for _ in range(pairs_needed):
            # Sample a random pair from Dust3R dataset
            max_attempts = 100
            for attempt in range(max_attempts):
                idx = random.randint(0, self.dust3r_length - 1)
                if idx not in used_indices:
                    used_indices.add(idx)
                    break
            else:
                # If we can't find unused index, just use random one
                idx = random.randint(0, self.dust3r_length - 1)
            
            try:
                pair_views = self.dust3r_dataset[idx]
                if isinstance(pair_views, list) and len(pair_views) >= 2:
                    views.extend(pair_views[:2])  # Add both views from the pair
                elif isinstance(pair_views, dict):
                    views.append(pair_views)  # Single view
            except Exception as e:
                logging.warning(f"Failed to load Dust3R pair {idx}: {e}")
                continue
                
            # Stop if we have enough views
            if len(views) >= target_length:
                break
        
        # Trim to exact target length
        if len(views) > target_length:
            views = views[:target_length]
        elif len(views) < target_length:
            # Duplicate last view if needed
            while len(views) < target_length:
                if views:
                    views.append(views[-1].copy())
                else:
                    # Fallback: create empty view
                    break
                    
        return views

    def get_data(
        self,
        seq_index: int = None,
        img_per_seq: int = None,
        seq_name: str = None,
        ids: list = None,
        aspect_ratio: float = 1.0,
    ) -> Dict:
        """
        Retrieve data in VGGT format by converting from Dust3R.
        
        Args:
            seq_index: Sequence index (used for random seed)
            img_per_seq: Number of images per sequence
            seq_name: Sequence name (ignored, generated automatically)
            ids: Frame IDs (ignored, generated automatically) 
            aspect_ratio: Target aspect ratio
            
        Returns:
            Dict containing sequence data in VGGT format
        """
        if img_per_seq is None:
            img_per_seq = self.sequence_length
            
        # Set random seed based on index for reproducibility
        if seq_index is not None:
            random.seed(self.seed + seq_index)
            np.random.seed(self.seed + seq_index)
        
        # Generate sequence from Dust3R pairs
        views = self._generate_sequence_from_pairs(img_per_seq)
        
        if not views:
            logging.error("Failed to generate any views from Dust3R dataset")
            raise RuntimeError("No views generated from Dust3R dataset")
        
        # Convert to VGGT format
        batch = self._dust3r_view_to_vggt_format(views)
        
        return batch


if __name__ == "__main__":
    # Test the adapter
    from types import SimpleNamespace
    
    # Create dummy config
    common_conf = SimpleNamespace()
    common_conf.img_size = 518
    common_conf.patch_size = 14
    common_conf.augs = SimpleNamespace()
    common_conf.augs.scales = [0.8, 1.2]
    common_conf.rescale = True
    common_conf.rescale_aug = True
    common_conf.landscape_check = True
    common_conf.seed = 42
    
    # Test with Co3d dataset (adjust path as needed)
    dataset_config = "Co3d(split='train', ROOT='/path/to/co3d', resolution=224)"
    
    try:
        adapter = Dust3RDatasetAdapter(
            common_conf=common_conf,
            dust3r_dataset_config=dataset_config,
            split='train',
            sequence_length=4
        )
        
        # Test data loading
        batch = adapter.get_data(seq_index=0, img_per_seq=4)
        print(f"Successfully loaded batch with {batch['frame_num']} frames")
        print(f"Image shapes: {[img.shape for img in batch['images']]}")
        print(f"Sequence name: {batch['seq_name']}")
        
    except Exception as e:
        print(f"Test failed: {e}")