import json
import os.path as osp
import os
import logging
import cv2
import random
import numpy as np
import torch
from pathlib import Path

from data.dataset_util import *
from data.base_dataset import BaseDataset


class RealEstate10KDataset(BaseDataset):
    """
    RealEstate10K dataset adapter for VGGT training and testing.
    
    RealEstate10K contains 80,000+ videos of real estate properties with
    camera poses and depth information. Each video contains multiple frames
    with known camera parameters.
    """
    
    def __init__(
        self,
        common_conf,
        split: str = "train",
        data_root: str = None,
        min_num_frames: int = 8,
        max_num_frames: int = 16,
        len_train: int = 50000,
        len_test: int = 10000,
        use_depth: bool = True,
        use_pose: bool = True,
    ):
        """
        Initialize the RealEstate10K dataset.
        
        Args:
            common_conf: Configuration object with common settings
            split: Dataset split ('train', 'val', 'test')
            data_root: Path to RealEstate10K data directory
            min_num_frames: Minimum number of frames per sequence
            max_num_frames: Maximum number of frames per sequence
            len_train: Length of training dataset
            len_test: Length of test dataset
            use_depth: Whether to load depth maps
            use_pose: Whether to load camera poses
        """
        super().__init__(common_conf=common_conf)
        
        self.debug = common_conf.debug
        self.training = common_conf.training
        self.get_nearby = common_conf.get_nearby
        self.load_depth = common_conf.load_depth and use_depth
        self.inside_random = common_conf.inside_random
        self.allow_duplicate_img = common_conf.allow_duplicate_img
        
        if data_root is None:
            raise ValueError("data_root must be specified for RealEstate10K dataset")
            
        self.data_root = data_root
        self.split = split
        self.min_num_frames = min_num_frames
        self.max_num_frames = max_num_frames
        self.use_pose = use_pose
        
        if split == "train":
            self.len_train = len_train
        elif split in ["val", "test"]:
            self.len_train = len_test
        else:
            raise ValueError(f"Invalid split: {split}")
            
        self._load_dataset_info()
        
    def _load_dataset_info(self):
        """Load dataset information and metadata."""
        logging.info(f"Loading RealEstate10K dataset from {self.data_root}")
        
        # Load split information
        split_file = osp.join(self.data_root, f"{self.split}_videos.txt")
        if not osp.exists(split_file):
            raise FileNotFoundError(f"Split file not found: {split_file}")
            
        with open(split_file, 'r') as f:
            self.video_list = [line.strip() for line in f.readlines()]
            
        # Load metadata
        metadata_file = osp.join(self.data_root, "metadata.json")
        if osp.exists(metadata_file):
            with open(metadata_file, 'r') as f:
                self.metadata = json.load(f)
        else:
            self.metadata = {}
            
        # Filter videos based on frame count
        self.valid_videos = []
        for video_id in self.video_list:
            video_path = osp.join(self.data_root, video_id)
            if osp.exists(video_path):
                frame_count = self._count_frames(video_path)
                if frame_count >= self.min_num_frames:
                    self.valid_videos.append(video_id)
                    
        logging.info(f"Found {len(self.valid_videos)} valid videos for {self.split} split")
        logging.info(f"Dataset length: {len(self)}")
        
    def _count_frames(self, video_path: str) -> int:
        """Count number of frames in a video directory."""
        frames_dir = osp.join(video_path, "frames")
        if osp.exists(frames_dir):
            return len([f for f in os.listdir(frames_dir) if f.endswith('.jpg')])
        return 0
        
    def _get_video_frames(self, video_id: str) -> list:
        """Get list of frame files for a video."""
        frames_dir = osp.join(self.data_root, video_id, "frames")
        if not osp.exists(frames_dir):
            return []
            
        frame_files = sorted([f for f in os.listdir(frames_dir) 
                            if f.endswith('.jpg') or f.endswith('.png')])
        return frame_files
        
    def _load_frame_data(self, video_id: str, frame_file: str) -> dict:
        """Load frame data including image, depth, and pose."""
        video_path = osp.join(self.data_root, video_id)
        frame_name = osp.splitext(frame_file)[0]
        
        # Load image
        image_path = osp.join(video_path, "frames", frame_file)
        if not osp.exists(image_path):
            raise FileNotFoundError(f"Image not found: {image_path}")
            
        image = read_image_cv2(image_path)
        
        # Load depth map
        depth_map = None
        if self.load_depth:
            depth_path = osp.join(video_path, "depths", f"{frame_name}.png")
            if osp.exists(depth_path):
                depth_map = read_depth(depth_path, 1.0)
                depth_map = threshold_depth_map(depth_map, min_percentile=-1, max_percentile=98)
            else:
                depth_map = np.zeros(image.shape[:2], dtype=np.float32)
                
        # Load camera pose
        extrinsic = np.eye(3, 4)
        intrinsic = np.eye(3)
        
        if self.use_pose:
            pose_path = osp.join(video_path, "poses", f"{frame_name}.txt")
            if osp.exists(pose_path):
                pose_data = np.loadtxt(pose_path)
                if pose_data.shape == (3, 4):
                    extrinsic = pose_data
                elif pose_data.shape == (4, 4):
                    extrinsic = pose_data[:3, :4]
                    
            # Load intrinsic parameters
            intrinsic_path = osp.join(video_path, "intrinsics", f"{frame_name}.txt")
            if osp.exists(intrinsic_path):
                intrinsic = np.loadtxt(intrinsic_path)
            else:
                # Default intrinsic parameters
                h, w = image.shape[:2]
                focal_length = max(h, w) * 0.8
                intrinsic[0, 0] = focal_length
                intrinsic[1, 1] = focal_length
                intrinsic[0, 2] = w / 2
                intrinsic[1, 2] = h / 2
                
        return {
            'image': image,
            'depth': depth_map,
            'extrinsic': extrinsic,
            'intrinsic': intrinsic,
            'frame_name': frame_name
        }
        
    def get_data(
        self,
        seq_index: int = None,
        img_per_seq: int = None,
        seq_name: str = None,
        ids: list = None,
        aspect_ratio: float = 1.0,
    ) -> dict:
        """
        Retrieve data for a specific sequence.
        
        Args:
            seq_index: Index of the sequence to retrieve
            img_per_seq: Number of images per sequence
            seq_name: Name of the sequence
            ids: Specific frame IDs to retrieve
            aspect_ratio: Aspect ratio for image processing
            
        Returns:
            dict: A batch of data including images, depths, and metadata
        """
        if self.inside_random:
            seq_index = random.randint(0, len(self.valid_videos) - 1)
            
        if seq_name is None:
            video_id = self.valid_videos[seq_index % len(self.valid_videos)]
        else:
            video_id = seq_name
            
        # Get frame files
        frame_files = self._get_video_frames(video_id)
        if len(frame_files) < self.min_num_frames:
            raise ValueError(f"Video {video_id} has insufficient frames: {len(frame_files)}")
            
        # Select frames
        if ids is None:
            num_frames = min(img_per_seq or self.max_num_frames, len(frame_files))
            selected_frames = np.random.choice(frame_files, num_frames, replace=self.allow_duplicate_img)
        else:
            selected_frames = [frame_files[i] for i in ids if i < len(frame_files)]
            
        target_image_shape = self.get_target_shape(aspect_ratio)
        
        images = []
        depths = []
        cam_points = []
        world_points = []
        point_masks = []
        extrinsics = []
        intrinsics = []
        image_paths = []
        original_sizes = []
        frame_names = []
        
        for frame_file in selected_frames:
            try:
                frame_data = self._load_frame_data(video_id, frame_file)
                
                image = frame_data['image']
                depth_map = frame_data['depth']
                extrinsic = frame_data['extrinsic']
                intrinsic = frame_data['intrinsic']
                frame_name = frame_data['frame_name']
                
                original_size = np.array(image.shape[:2])
                
                # Process image and depth
                (
                    processed_image,
                    processed_depth,
                    processed_extrinsic,
                    processed_intrinsic,
                    world_coords_points,
                    cam_coords_points,
                    point_mask,
                    _,
                ) = self.process_one_image(
                    image,
                    depth_map,
                    extrinsic,
                    intrinsic,
                    original_size,
                    target_image_shape,
                    filepath=osp.join(video_id, frame_file),
                )
                
                images.append(processed_image)
                depths.append(processed_depth)
                extrinsics.append(processed_extrinsic)
                intrinsics.append(processed_intrinsic)
                cam_points.append(cam_coords_points)
                world_points.append(world_coords_points)
                point_masks.append(point_mask)
                image_paths.append(osp.join(self.data_root, video_id, "frames", frame_file))
                original_sizes.append(original_size)
                frame_names.append(frame_name)
                
            except Exception as e:
                logging.warning(f"Failed to load frame {frame_file} from {video_id}: {e}")
                continue
                
        if len(images) == 0:
            raise ValueError(f"No valid frames loaded from video {video_id}")
            
        batch = {
            "seq_name": f"realestate10k_{video_id}",
            "ids": list(range(len(images))),
            "frame_num": len(images),
            "images": images,
            "depths": depths,
            "extrinsics": extrinsics,
            "intrinsics": intrinsics,
            "cam_points": cam_points,
            "world_points": world_points,
            "point_masks": point_masks,
            "original_sizes": original_sizes,
            "frame_names": frame_names,
        }
        
        return batch
        
    def __len__(self):
        """Return the length of the dataset."""
        return self.len_train 