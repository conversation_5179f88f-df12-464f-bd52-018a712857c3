# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import os
import os.path as osp
import json
import logging
import random
import numpy as np
import cv2
from glob import glob

from data.dataset_util import *
from data.base_dataset import BaseDataset


class ScanNetDataset(BaseDataset):
    """
    ScanNet dataset class for VGGT testing.
    
    This dataset handles the ScanNet-1500 test dataset format, loading RGB images,
    depth maps, and camera parameters for 3D scene reconstruction evaluation.
    """
    
    def __init__(
        self,
        common_conf,
        split: str = "test",
        scannet_dir: str = None,
        min_num_images: int = 8,
        max_num_images: int = 16,
        len_test: int = 1500,
        use_depth: bool = True,
        use_pose: bool = True,
        scene_list_file: str = None,
    ):
        """
        Initialize the ScanNet dataset.

        Args:
            common_conf: Configuration object with common settings.
            split (str): Dataset split, should be 'test' for ScanNet-1500.
            scannet_dir (str): Directory path to ScanNet data.
            min_num_images (int): Minimum number of images per sequence.
            max_num_images (int): Maximum number of images per sequence.
            len_test (int): Length of the test dataset.
            use_depth (bool): Whether to load depth maps.
            use_pose (bool): Whether to load camera poses.
            scene_list_file (str): Optional file containing list of scenes to use.
        
        Raises:
            ValueError: If scannet_dir is not specified or invalid split.
        """
        super().__init__(common_conf=common_conf)

        self.debug = getattr(common_conf, 'debug', False)
        self.training = getattr(common_conf, 'training', False)
        self.get_nearby = getattr(common_conf, 'get_nearby', True)
        self.load_depth = use_depth
        self.use_pose = use_pose
        self.inside_random = getattr(common_conf, 'inside_random', False)
        self.allow_duplicate_img = getattr(common_conf, 'allow_duplicate_img', False)

        if scannet_dir is None:
            raise ValueError("scannet_dir must be specified.")

        if split != "test":
            raise ValueError(f"Only 'test' split is supported for ScanNet, got: {split}")

        self.scannet_dir = scannet_dir
        self.min_num_images = min_num_images
        self.max_num_images = max_num_images
        
        # Load scene information
        self.data_store = {}
        self.sequence_list = []
        
        self._load_scannet_data(scene_list_file)
        
        # Set len_train based on actual loaded data or fallback to len_test
        self.len_train = min(len_test, len(self.sequence_list)) if len(self.sequence_list) > 0 else len_test
        
        status = "Testing"
        logging.info(f"{status}: ScanNet Data size: {len(self.sequence_list)}")
        logging.info(f"{status}: ScanNet Data dataset length: {len(self)}")

    def _load_scannet_data(self, scene_list_file=None):
        """
        Load ScanNet scene data and metadata.
        
        Args:
            scene_list_file (str): Optional file containing list of scenes to use.
        """
        if scene_list_file and osp.exists(scene_list_file):
            # Load specific scenes from file
            with open(scene_list_file, 'r') as f:
                scene_names = [line.strip() for line in f if line.strip()]
        else:
            # Auto-discover scenes in the directory
            scene_names = []
            if osp.exists(self.scannet_dir):
                for item in os.listdir(self.scannet_dir):
                    scene_path = osp.join(self.scannet_dir, item)
                    if osp.isdir(scene_path) and item.startswith('scene'):
                        scene_names.append(item)
            scene_names.sort()
        
        if self.debug:
            # Limit to first few scenes for debugging
            scene_names = scene_names[:3]
        
        total_frame_num = 0
        
        for scene_name in scene_names:
            scene_path = osp.join(self.scannet_dir, scene_name)
            
            if not osp.exists(scene_path):
                logging.warning(f"Scene directory not found: {scene_path}")
                continue
            
            # Load scene metadata
            scene_data = self._load_scene_metadata(scene_path, scene_name)
            
            if len(scene_data) < self.min_num_images:
                logging.warning(f"Scene {scene_name} has only {len(scene_data)} images, skipping")
                continue
            
            self.data_store[scene_name] = scene_data
            self.sequence_list.append(scene_name)
            total_frame_num += len(scene_data)
        
        self.sequence_list_len = len(self.sequence_list)
        self.total_frame_num = total_frame_num
        
        if self.sequence_list_len == 0:
            raise ValueError(f"No valid scenes found in {self.scannet_dir}")

    def _load_scene_metadata(self, scene_path, scene_name):
        """
        Load metadata for a single scene.
        
        Args:
            scene_path (str): Path to the scene directory.
            scene_name (str): Name of the scene.
            
        Returns:
            list: List of frame metadata dictionaries.
        """
        scene_data = []
        
        color_dir = osp.join(scene_path, 'color')
        depth_dir = osp.join(scene_path, 'depth')
        pose_dir = osp.join(scene_path, 'pose')
        intrinsic_file = osp.join(scene_path, 'intrinsic', 'intrinsic_color.txt')
        
        if not osp.exists(color_dir):
            color_dir = osp.join(scene_path, 'rgb')
        if not osp.exists(intrinsic_file):
            intrinsic_file = osp.join(scene_path, 'camera-intrinsics.txt')
        
        if not osp.exists(color_dir):
            logging.warning(f"No color/rgb directory found in {scene_path}")
            return scene_data
        
        intrinsic_matrix = self._load_intrinsic_matrix(intrinsic_file)
        if intrinsic_matrix is None:
            logging.warning(f"Could not load intrinsic matrix for {scene_name}")
            return scene_data
        
        color_files = glob(osp.join(color_dir, '*.jpg')) + glob(osp.join(color_dir, '*.png'))
        color_files.sort()
        
        for color_file in color_files:
            frame_id = osp.splitext(osp.basename(color_file))[0]
            
            depth_file = None
            pose_file = None
            
            if self.load_depth and osp.exists(depth_dir):
                depth_file = osp.join(depth_dir, f"{frame_id}.png")
                if not osp.exists(depth_file):
                    depth_file = osp.join(depth_dir, f"{frame_id}.jpg")
                if not osp.exists(depth_file):
                    depth_file = None
            
            if self.use_pose and osp.exists(pose_dir):
                pose_file = osp.join(pose_dir, f"{frame_id}.txt")
                if not osp.exists(pose_file):
                    pose_file = None
            
            # Create frame metadata
            frame_data = {
                'frame_id': frame_id,
                'color_file': color_file,
                'depth_file': depth_file,
                'pose_file': pose_file,
                'intrinsic': intrinsic_matrix.copy(),
                'scene_name': scene_name,
            }
            
            scene_data.append(frame_data)
        
        return scene_data

    def _load_intrinsic_matrix(self, intrinsic_file):
        """
        Load camera intrinsic matrix from file.

        Args:
            intrinsic_file (str): Path to intrinsic parameters file.

        Returns:
            np.ndarray: 3x3 intrinsic matrix or None if loading fails.
        """
        if not osp.exists(intrinsic_file):
            return None

        try:
            if intrinsic_file.endswith('.txt'):
                with open(intrinsic_file, 'r') as f:
                    lines = [line.strip() for line in f if line.strip()]

                if len(lines) == 1:
                    # Single line format: fx fy cx cy
                    values = list(map(float, lines[0].split()))
                    if len(values) >= 4:
                        fx, fy, cx, cy = values[:4]
                        intrinsic = np.array([
                            [fx, 0, cx],
                            [0, fy, cy],
                            [0, 0, 1]
                        ], dtype=np.float32)
                        return intrinsic
                elif len(lines) >= 3:
                    matrix_data = []
                    for line in lines[:3]:
                        row = list(map(float, line.split()))
                        if len(row) >= 3:
                            matrix_data.append(row[:3])

                    if len(matrix_data) == 3:
                        intrinsic = np.array(matrix_data, dtype=np.float32)
                        return intrinsic

            intrinsic = np.loadtxt(intrinsic_file)
            if intrinsic.shape == (4, 4):
                return intrinsic[:3, :3].astype(np.float32)
            elif intrinsic.shape == (3, 3):
                return intrinsic.astype(np.float32)

        except Exception as e:
            logging.warning(f"Failed to load intrinsic matrix from {intrinsic_file}: {e}")

        return None

    def _load_pose_matrix(self, pose_file):
        """
        Load camera pose matrix from file.

        Args:
            pose_file (str): Path to pose file.

        Returns:
            np.ndarray: 4x4 pose matrix or None if loading fails.
        """
        if not osp.exists(pose_file):
            return None

        try:
            pose_matrix = np.loadtxt(pose_file)
            if pose_matrix.shape == (4, 4):
                return pose_matrix.astype(np.float32)
        except Exception as e:
            logging.warning(f"Failed to load pose matrix from {pose_file}: {e}")

        return None

    def get_data(
        self,
        seq_index: int = None,
        img_per_seq: int = None,
        seq_name: str = None,
        ids: list = None,
        aspect_ratio: float = 1.0,
    ) -> dict:
        """
        Retrieve data for a specific sequence.

        Args:
            seq_index (int): Index of the sequence to retrieve.
            img_per_seq (int): Number of images per sequence.
            seq_name (str): Name of the sequence.
            ids (list): Specific IDs to retrieve.
            aspect_ratio (float): Aspect ratio for image processing.

        Returns:
            dict: A batch of data including images, depths, and other metadata.
        """
        if self.inside_random:
            seq_index = random.randint(0, self.sequence_list_len - 1)

        if seq_index is None or seq_index >= self.sequence_list_len:
            if self.sequence_list_len > 0:
                seq_index = seq_index % self.sequence_list_len
            else:
                raise IndexError(f"No sequences available in dataset")

        if seq_name is None:
            seq_name = self.sequence_list[seq_index]

        metadata = self.data_store[seq_name]

        if ids is None:
            if img_per_seq is None:
                img_per_seq = min(self.max_num_images, len(metadata))

            if self.get_nearby and len(metadata) > img_per_seq:
                start_idx = random.randint(0, len(metadata) - img_per_seq)
                ids = list(range(start_idx, start_idx + img_per_seq))
            else:
                ids = np.random.choice(
                    len(metadata),
                    min(img_per_seq, len(metadata)),
                    replace=self.allow_duplicate_img
                )

        annos = [metadata[i] for i in ids]
        target_image_shape = self.get_target_shape(aspect_ratio)

        images = []
        depths = []
        cam_points = []
        world_points = []
        point_masks = []
        extrinsics = []
        intrinsics = []
        image_paths = []
        original_sizes = []

        for anno in annos:
            image = read_image_cv2(anno['color_file'])
            original_size = np.array(image.shape[:2])

            depth_map = None
            if self.load_depth and anno['depth_file'] is not None:
                depth_map = self._load_depth_map(anno['depth_file'])

            if depth_map is None:
                depth_map = np.zeros(original_size, dtype=np.float32)

            intrinsic_matrix = anno['intrinsic']

            if self.use_pose and anno['pose_file'] is not None:
                pose_matrix = self._load_pose_matrix(anno['pose_file'])
                if pose_matrix is not None:
                    extrinsic_matrix = pose_matrix
                else:
                    extrinsic_matrix = np.eye(4, dtype=np.float32)
            else:
                extrinsic_matrix = np.eye(4, dtype=np.float32)

            (
                processed_image,
                processed_depth,
                updated_extrinsic,
                updated_intrinsic,
                world_coords_points,
                cam_coords_points,
                point_mask,
                _,
            ) = self.process_one_image(
                image,
                depth_map,
                extrinsic_matrix,
                intrinsic_matrix,
                original_size,
                target_image_shape,
                filepath=anno['color_file'],
            )

            images.append(processed_image)
            depths.append(processed_depth)
            extrinsics.append(updated_extrinsic)
            intrinsics.append(updated_intrinsic)
            cam_points.append(cam_coords_points)
            world_points.append(world_coords_points)
            point_masks.append(point_mask)
            image_paths.append(anno['color_file'])
            original_sizes.append(original_size)

        batch = {
            "seq_name": f"scannet_{seq_name}",
            "ids": np.array(ids, dtype=np.int64),  # Ensure int64 type
            "frame_num": len(extrinsics),
            "images": images,
            "depths": depths,
            "extrinsics": np.stack(extrinsics).astype(np.float32),  # Ensure float32 type
            "intrinsics": np.stack(intrinsics).astype(np.float32),  # Ensure float32 type
            "cam_points": cam_points,
            "world_points": world_points,
            "point_masks": point_masks,
            "original_sizes": original_sizes,
            "image_paths": image_paths,
        }
        return batch

    def _load_depth_map(self, depth_file):
        """
        Load depth map from file.

        Args:
            depth_file (str): Path to depth file.

        Returns:
            np.ndarray: Depth map or None if loading fails.
        """
        try:
            if depth_file.endswith('.png'):
                depth_img = cv2.imread(depth_file, cv2.IMREAD_ANYDEPTH)
                if depth_img is not None:
                    depth_map = depth_img.astype(np.float32) / 1000.0  # Convert to meters
                    return depth_map
            elif depth_file.endswith('.exr'):
                depth_map = cv2.imread(depth_file, cv2.IMREAD_ANYDEPTH)
                if depth_map is not None:
                    return depth_map.astype(np.float32)
            else:
                depth_map = cv2.imread(depth_file, cv2.IMREAD_ANYDEPTH)
                if depth_map is not None:
                    return depth_map.astype(np.float32)
        except Exception as e:
            logging.warning(f"Failed to load depth map from {depth_file}: {e}")

        return None
