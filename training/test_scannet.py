#!/usr/bin/env python3
# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

"""
ScanNet Testing Script for VGGT Model

This script evaluates the pre-trained VGGT model on the ScanNet-1500 test dataset,
computing performance metrics for camera pose estimation, depth prediction, and 
3D point cloud reconstruction following the VGGT paper evaluation protocol.

Usage:
    python test_scannet.py [--config scannet_test] [--checkpoint /path/to/model.pt] [--output_dir /path/to/output]
"""

import os
import sys
import argparse
import logging
import json
import time
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional

import torch
import torch.distributed as dist
import numpy as np
from hydra import initialize, compose
from omegaconf import DictConfig, OmegaConf

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'training'))

from trainer import Trainer
from vggt.models.vggt import VGGT
from vggt.utils.pose_enc import pose_encoding_to_extri_intri
from vggt.utils.geometry import unproject_depth_map_to_point_map
from evaluation.scannet_evaluator import ScanNetEvaluator


class CustomScanNetTrainer(Trainer):
    """
    Custom trainer for ScanNet testing that captures predictions during validation.
    Improved to handle VGGT evaluation requirements properly.
    """
    
    def __init__(self, tester=None, **kwargs):
        """
        Initialize the custom trainer.
        
        Args:
            tester: ScanNetTester instance to receive predictions
            **kwargs: Arguments passed to base Trainer
        """
        super().__init__(**kwargs)
        self.tester = tester
    
    def _step(self, batch, model, phase: str, loss_meters: dict):
        """
        Override _step to capture predictions during validation.
        Enhanced with better error handling and data processing.
        """
        try:
            batch = self._prepare_batch_data(batch)
            
            y_hat = model(images=batch["images"])
            
            loss_dict = self.loss(y_hat, batch)
            
            if phase == 'val' and self.tester is not None:
                self.tester._collect_batch_results(y_hat, batch, loss_dict)
            
            log_data = {**y_hat, **loss_dict, **batch}

            self._update_and_log_scalars(log_data, phase, self.steps[phase], loss_meters)
            self._log_tb_visuals(log_data, phase, self.steps[phase])

            self.steps[phase] += 1
            return loss_dict
            
        except Exception as e:
            logging.error(f"Error in training step: {e}")
            logging.debug(traceback.format_exc())
            return {"loss_objective": torch.tensor(0.0, requires_grad=True)}
    
    def _prepare_batch_data(self, batch: Dict) -> Dict:
        """
        Prepare batch data ensuring proper tensor formats and shapes.
        
        Args:
            batch: Input batch dictionary
            
        Returns:
            Dict: Prepared batch with proper tensor formats
        """

        prepared_batch = {}
        
        for key, value in batch.items():
            if key == "images":
                if isinstance(value, list):
                    images_tensor = torch.stack([
                        torch.from_numpy(img).float() if isinstance(img, np.ndarray) else img.float()
                        for img in value
                    ])
                    if len(images_tensor.shape) == 4:  # (N, C, H, W)
                        images_tensor = images_tensor.unsqueeze(0)
                    # Ensure tensor is contiguous and in correct format
                    prepared_batch[key] = images_tensor.contiguous()
                elif isinstance(value, torch.Tensor):
                    prepared_batch[key] = value.float()
                else:
                    prepared_batch[key] = value
            elif key in ["depths", "extrinsics", "intrinsics", "world_points", "point_masks"]:
                # Convert other tensor data with proper type handling
                if isinstance(value, np.ndarray):
                    if key in ["extrinsics", "intrinsics", "depths", "world_points"]:
                        prepared_batch[key] = torch.from_numpy(value).float()
                    elif key in ["point_masks"]:
                        prepared_batch[key] = torch.from_numpy(value).bool()
                    else:
                        prepared_batch[key] = torch.from_numpy(value)
                elif isinstance(value, list):
                    try:
                        stacked = np.stack(value)
                        if key in ["extrinsics", "intrinsics", "depths", "world_points"]:
                            prepared_batch[key] = torch.from_numpy(stacked).float()
                        elif key in ["point_masks"]:
                            prepared_batch[key] = torch.from_numpy(stacked).bool()
                        else:
                            prepared_batch[key] = torch.from_numpy(stacked)
                    except:
                        prepared_batch[key] = value
                elif isinstance(value, torch.Tensor):
                    if key in ["extrinsics", "intrinsics", "depths", "world_points"]:
                        prepared_batch[key] = value.float()
                    elif key in ["point_masks"]:
                        prepared_batch[key] = value.bool()
                    else:
                        prepared_batch[key] = value
                else:
                    prepared_batch[key] = value
        
        return prepared_batch


class ScanNetTester:
    """
    ScanNet testing class that handles model loading, inference, and evaluation.
    Enhanced for VGGT paper evaluation requirements.
    """
    
    def __init__(self, config: DictConfig, checkpoint_path: str = None, output_dir: str = None):
        """
        Initialize the ScanNet tester.
        
        Args:
            config: Hydra configuration object
            checkpoint_path: Optional path to model checkpoint
            output_dir: Optional output directory for results
        """
        self.config = config
        self.checkpoint_path = checkpoint_path or config.checkpoint.resume_checkpoint_path
        self.output_dir = output_dir or config.get('scannet_eval', {}).get('save_dir', 'scannet_results')
        os.makedirs(self.output_dir, exist_ok=True)
        self._setup_logging()
        
        self.evaluator = ScanNetEvaluator(
            save_predictions=config.get('scannet_eval', {}).get('save_predictions', True),
            save_visualizations=config.get('scannet_eval', {}).get('save_images', True),
            output_dir=self.output_dir
        )
        
        self.results = {
            'scenes': {},
            'metrics': {},
            'config': OmegaConf.to_yaml(config),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'vggt_eval_version': '1.1'
        }
        
        self.batch_predictions = []
        self.batch_data = []
        
        logging.info(f"ScanNet Tester initialized (v1.1)")
        logging.info(f"Checkpoint: {self.checkpoint_path}")
        logging.info(f"Output directory: {self.output_dir}")

    def _setup_logging(self):
        """Setup logging configuration."""
        log_file = os.path.join(self.output_dir, 'test_log.txt')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )

    def run_testing(self):
        """
        Run the complete testing pipeline.
        """
        logging.info("Starting VGGT ScanNet-1500 evaluation...")
        
        try:
            self._validate_setup()
            
            trainer = CustomScanNetTrainer(**self.config, tester=self)
            
            logging.info("Running model inference on ScanNet test data...")
            trainer.run_val()
            
            self._compute_metrics()
            
            self._save_results()
            
            logging.info("ScanNet testing completed successfully!")
            
        except Exception as e:
            logging.error(f"Testing failed with error: {e}")
            import traceback
            logging.error(f"Full traceback: {traceback.format_exc()}")
            raise
    
    def _validate_setup(self):
        """Validate that all required components are available."""
        # Check checkpoint exists
        if not os.path.exists(self.checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found: {self.checkpoint_path}")
        
        data_dir = self.config.data.val.dataset.dataset_configs[0].scannet_dir
        if not os.path.exists(data_dir):
            raise FileNotFoundError(f"ScanNet data directory not found: {data_dir}")
        
        logging.info("✓ Setup validation passed")

    def _collect_batch_results(self, predictions: Dict, batch: Dict, loss_dict: Dict):
        """
        Collect batch results during validation for evaluation.
        Enhanced with better error handling and data validation.
        
        Args:
            predictions: Model predictions
            batch: Ground truth batch data
            loss_dict: Computed losses
        """
        # Extract scene name from batch with improved handling
        seq_name = self._extract_scene_name(batch)
        
        # Validate that we have all required data
        if not self._validate_batch_data(batch, seq_name):
            return
        
        # Validate predictions
        if not self._validate_predictions(predictions, seq_name):
            return
        
        logging.info(f"Processing scene: {seq_name}")
        
        # Evaluate this batch using the evaluator
        try:
            batch_results = self.evaluator.evaluate_batch(predictions, batch, seq_name)
            logging.info(f"✓ Evaluated scene: {seq_name}")
            
            # Log key metrics for this scene
            self._log_scene_metrics(seq_name, batch_results)
            
        except Exception as e:
            import traceback
            logging.error(f"✗ Failed to evaluate scene {seq_name}: {e}")
            logging.error(f"Full traceback: {traceback.format_exc()}")
            # Don't re-raise to allow other scenes to be processed
    
    def _extract_scene_name(self, batch: Dict) -> str:
        """Extract scene name from batch with multiple fallback methods."""
        # Method 1: seq_name field
        if 'seq_name' in batch:
            seq_name = batch['seq_name']
            if isinstance(seq_name, list):
                seq_name = seq_name[0]
            # Remove 'scannet_' prefix if present
            if isinstance(seq_name, str) and seq_name.startswith('scannet_'):
                seq_name = seq_name[7:]
            return seq_name
        
        # Method 2: scene_id field
        if 'scene_id' in batch:
            scene_id = batch['scene_id']
            if isinstance(scene_id, list):
                scene_id = scene_id[0]
            return scene_id
        
        # Method 3: From image paths
        if 'image_paths' in batch and batch['image_paths']:
            path = batch['image_paths'][0] if isinstance(batch['image_paths'], list) else batch['image_paths']
            if isinstance(path, str):
                # Extract scene name from path like .../scene0000_00/...
                import re
                match = re.search(r'scene\d+_\d+', path)
                if match:
                    return match.group(0)
        
        # Fallback: Unknown scene
        return "unknown_scene"
    
    def _validate_batch_data(self, batch: Dict, seq_name: str) -> bool:
        """Validate that batch contains all required fields."""
        required_fields = ['extrinsics', 'intrinsics', 'depths', 'world_points', 'point_masks']
        
        for field in required_fields:
            if field not in batch:
                logging.warning(f"Scene {seq_name} missing required field: {field}")
                return False
        
        # Check frame_num field with fallback
        if 'frame_num' not in batch:
            # Try to infer frame count
            if 'images' in batch:
                if isinstance(batch['images'], list):
                    batch['frame_num'] = len(batch['images'])
                elif isinstance(batch['images'], torch.Tensor):
                    # Get sequence dimension
                    if len(batch['images'].shape) >= 2:
                        batch['frame_num'] = batch['images'].shape[1] if len(batch['images'].shape) == 5 else batch['images'].shape[0]
                    else:
                        batch['frame_num'] = 1
                else:
                    batch['frame_num'] = 1
                logging.info(f"Inferred frame_num={batch['frame_num']} for scene {seq_name}")
            else:
                batch['frame_num'] = 1
                logging.warning(f"Could not determine frame count for scene {seq_name}, defaulting to 1")
        
        return True
    
    def _validate_predictions(self, predictions: Dict, seq_name: str) -> bool:
        """Validate that predictions contain expected fields."""
        if 'pose_enc_list' not in predictions:
            logging.warning(f"Scene {seq_name} predictions missing pose_enc_list")
            return False
        
        if 'depth' not in predictions and 'world_points' not in predictions:
            logging.warning(f"Scene {seq_name} predictions missing both depth and world_points")
            return False
        
        return True
    
    def _log_scene_metrics(self, scene_name: str, results: Dict):
        """Log key metrics for a scene."""
        if 'pose_metrics' in results and results['pose_metrics']:
            pose_metrics = results['pose_metrics']
            if 'translation_error_mean' in pose_metrics:
                logging.info(f"  {scene_name} - Pose: T_err={pose_metrics['translation_error_mean']:.3f}m, "
                           f"R_err={pose_metrics['rotation_error_mean']:.2f}°, "
                           f"Success(strict)={pose_metrics.get('success_rate_strict', 0):.1%}, "
                           f"Success(std)={pose_metrics.get('success_rate_standard', 0):.1%}")

    def _compute_metrics(self):
        """
        Compute final evaluation metrics from all collected results.
        """
        logging.info("Computing final evaluation metrics...")
        
        # Get final metrics from evaluator
        final_metrics = self.evaluator.compute_final_metrics()
        self.results['metrics'] = final_metrics
        
        # Log key metrics following VGGT paper format
        logging.info("=== FINAL EVALUATION RESULTS ===")
        
        if 'camera_pose' in final_metrics:
            pose_metrics = final_metrics['camera_pose']
            logging.info("Camera Pose Estimation:")
            logging.info(f"  Translation Error: {pose_metrics.get('translation_error_mean', 0):.3f} ± {pose_metrics.get('translation_error_std', 0):.3f}m (median: {pose_metrics.get('translation_error_median', 0):.3f}m)")
            logging.info(f"  Rotation Error: {pose_metrics.get('rotation_error_mean', 0):.2f} ± {pose_metrics.get('rotation_error_std', 0):.2f}° (median: {pose_metrics.get('rotation_error_median', 0):.2f}°)")
            logging.info(f"  Success Rate (Strict <0.25m & <2°): {pose_metrics.get('success_rate_strict', 0):.1%}")
            logging.info(f"  Success Rate (Standard <0.5m & <5°): {pose_metrics.get('success_rate_standard', 0):.1%}")
            logging.info(f"  Success Rate (Loose <1.0m & <10°): {pose_metrics.get('success_rate_loose', 0):.1%}")
            logging.info(f"  Total Valid Poses: {pose_metrics.get('total_valid_poses', 0)}")
        
        if 'depth' in final_metrics:
            depth_metrics = final_metrics['depth']
            logging.info("Depth Estimation:")
            logging.info(f"  Abs Rel Error: {depth_metrics.get('abs_rel', 0):.3f}")
            logging.info(f"  RMSE: {depth_metrics.get('rmse', 0):.3f}")
            logging.info(f"  Accuracy (δ < 1.25): {depth_metrics.get('accuracy_1_25', 0):.1%}")
        
        if 'point_cloud' in final_metrics:
            pc_metrics = final_metrics['point_cloud']
            logging.info("Point Cloud Reconstruction:")
            logging.info(f"  Chamfer Distance: {pc_metrics.get('chamfer_distance', 0):.4f}")
            logging.info(f"  F-Score (1cm): {pc_metrics.get('f_score_0.01', 0):.1%}")
            logging.info(f"  Completeness: {pc_metrics.get('completeness', 0):.1%}")
        
        logging.info("================================")

    def _save_results(self):
        """
        Save all results to files.
        """
        logging.info("Saving results...")
        
        # Add scene count information
        self.results['scenes'] = {
            'total_scenes': len(self.evaluator.scene_results),
            'processed_scenes': len(self.evaluator.scene_results),
            'failed_scenes': 0,  # We'll count based on successful evaluations
        }
        
        # Save main results as JSON
        results_file = os.path.join(self.output_dir, 'scannet_test_results.json')
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Save detailed results using evaluator
        detailed_results_file = os.path.join(self.output_dir, 'detailed_results.json')
        self.evaluator.save_results(detailed_results_file)
        
        # Save metrics summary
        metrics_file = os.path.join(self.output_dir, 'metrics_summary.txt')
        with open(metrics_file, 'w') as f:
            f.write("ScanNet-1500 Test Results Summary\n")
            f.write("=" * 40 + "\n\n")
            
            metrics = self.results['metrics']
            
            if 'camera_pose' in metrics:
                pose_metrics = metrics['camera_pose']
                f.write("Camera Pose Estimation:\n")
                f.write(f"  Translation Error (mean): {pose_metrics.get('translation_error_mean', 0):.3f} m\n")
                f.write(f"  Translation Error (std): {pose_metrics.get('translation_error_std', 0):.3f} m\n")
                f.write(f"  Translation Error (median): {pose_metrics.get('translation_error_median', 0):.3f} m\n")
                f.write(f"  Rotation Error (mean): {pose_metrics.get('rotation_error_mean', 0):.2f} degrees\n")
                f.write(f"  Rotation Error (std): {pose_metrics.get('rotation_error_std', 0):.2f} degrees\n")
                f.write(f"  Rotation Error (median): {pose_metrics.get('rotation_error_median', 0):.2f} degrees\n")
                f.write(f"  Success Rate (Strict <0.25m & <2°): {pose_metrics.get('success_rate_strict', 0):.1%}\n")
                f.write(f"  Success Rate (Standard <0.5m & <5°): {pose_metrics.get('success_rate_standard', 0):.1%}\n")
                f.write(f"  Success Rate (Loose <1.0m & <10°): {pose_metrics.get('success_rate_loose', 0):.1%}\n")
                f.write(f"  Total Valid Poses: {pose_metrics.get('total_valid_poses', 0)}\n\n")
            
            if 'depth' in metrics:
                depth_metrics = metrics['depth']
                f.write("Depth Estimation:\n")
                f.write(f"  Absolute Relative Error: {depth_metrics.get('abs_rel', 0):.3f}\n")
                f.write(f"  Square Rel Error: {depth_metrics.get('sq_rel', 0):.3f}\n")
                f.write(f"  RMSE: {depth_metrics.get('rmse', 0):.3f}\n")
                f.write(f"  RMSE Log: {depth_metrics.get('rmse_log', 0):.3f}\n")
                f.write(f"  Accuracy (δ < 1.25): {depth_metrics.get('accuracy_1_25', 0):.1%}\n")
                f.write(f"  Accuracy (δ < 1.25²): {depth_metrics.get('accuracy_1_25^2', 0):.1%}\n")
                f.write(f"  Accuracy (δ < 1.25³): {depth_metrics.get('accuracy_1_25^3', 0):.1%}\n\n")
            
            if 'point_cloud' in metrics:
                pc_metrics = metrics['point_cloud']
                f.write("3D Point Cloud Reconstruction:\n")
                f.write(f"  Chamfer Distance: {pc_metrics.get('chamfer_distance', 0):.4f}\n")
                f.write(f"  F-Score (0.01m): {pc_metrics.get('f_score_0.01', 0):.1%}\n")
                f.write(f"  F-Score (0.02m): {pc_metrics.get('f_score_0.02', 0):.1%}\n")
                f.write(f"  Completeness: {pc_metrics.get('completeness', 0):.1%}\n")
                f.write(f"  Accuracy: {pc_metrics.get('accuracy', 0):.1%}\n")
        
        logging.info(f"Results saved to {self.output_dir}")
        logging.info(f"Detailed results: {detailed_results_file}")
        logging.info(f"Summary: {metrics_file}")


def main():
    """Main function to run ScanNet testing."""
    parser = argparse.ArgumentParser(description="Test VGGT model on ScanNet-1500 dataset")
    parser.add_argument(
        "--config", 
        type=str, 
        default="scannet_test",
        help="Name of the config file (without .yaml extension)"
    )
    parser.add_argument(
        "--checkpoint",
        type=str,
        default=None,
        help="Path to model checkpoint (overrides config)"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default=None,
        help="Output directory for results (overrides config)"
    )
    parser.add_argument(
        "--data_dir",
        type=str,
        default=None,
        help="ScanNet data directory (overrides config)"
    )
    
    args = parser.parse_args()

    # Load configuration
    with initialize(version_base=None, config_path="config"):
        cfg = compose(config_name=args.config)
    
    # Override config with command line arguments
    if args.checkpoint:
        cfg.checkpoint.resume_checkpoint_path = args.checkpoint
    
    if args.data_dir:
        cfg.data.val.dataset.dataset_configs[0].scannet_dir = args.data_dir
    
    # Initialize and run tester
    tester = ScanNetTester(cfg, args.checkpoint, args.output_dir)
    tester.run_testing()


if __name__ == "__main__":
    main()
