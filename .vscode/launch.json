// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug 单卡 Rank0",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/training/test_scannet.py",
      "console": "integratedTerminal",
      "args": [
        "--config", "scannet_test.yaml"
      ],
      "env": {
        "CUDA_VISIBLE_DEVICES": "0",
        "PYTHONUNBUFFERED": "1"
      }
    }
  ]
}
