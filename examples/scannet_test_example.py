#!/usr/bin/env python3
"""
ScanNet Testing Example

This script demonstrates how to use the VGGT ScanNet testing functionality
with different configurations and options.
"""

import os
import sys
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'training'))

from hydra import initialize, compose
from omegaconf import DictConfig, OmegaConf
from training.utils.scannet_utils import check_scannet_data_structure


def example_basic_testing():
    """
    Example 1: Basic ScanNet testing with default configuration
    """
    print("=== Example 1: Basic ScanNet Testing ===")
    
    # This is equivalent to running: python test_scannet.py
    from test_scannet import ScanNetTester
    
    # Load default configuration
    with initialize(version_base=None, config_path="../training/config"):
        cfg = compose(config_name="scannet_test")
    
    # Initialize and run tester
    tester = ScanNetTester(cfg)
    print(f"Tester initialized with checkpoint: {tester.checkpoint_path}")
    print(f"Output directory: {tester.output_dir}")
    
    # Note: Uncomment the next line to actually run testing
    # tester.run_testing()
    
    print("Basic testing setup complete!\n")


def example_custom_paths():
    """
    Example 2: Testing with custom paths and settings
    """
    print("=== Example 2: Custom Paths and Settings ===")
    
    # Custom paths
    custom_checkpoint = "/path/to/your/custom/model.pt"
    custom_data_dir = "/path/to/your/scannet/data"
    custom_output_dir = "./custom_scannet_results"
    
    # Load and modify configuration
    with initialize(version_base=None, config_path="../training/config"):
        cfg = compose(config_name="scannet_test")
    
    # Override configuration
    cfg.checkpoint.resume_checkpoint_path = custom_checkpoint
    cfg.data.val.dataset.dataset_configs[0].scannet_dir = custom_data_dir
    
    # Reduce memory usage for testing
    cfg.max_img_per_gpu = 8
    cfg.data.val.dataset.dataset_configs[0].max_num_images = 8
    cfg.limit_val_batches = 50  # Process only 50 batches for quick testing
    
    print("Modified configuration:")
    print(f"  Checkpoint: {cfg.checkpoint.resume_checkpoint_path}")
    print(f"  Data directory: {cfg.data.val.dataset.dataset_configs[0].scannet_dir}")
    print(f"  Max images per GPU: {cfg.max_img_per_gpu}")
    print(f"  Batch limit: {cfg.limit_val_batches}")
    
    # Initialize tester with custom settings
    from test_scannet import ScanNetTester
    tester = ScanNetTester(cfg, output_dir=custom_output_dir)
    
    print("Custom configuration setup complete!\n")


def example_data_validation():
    """
    Example 3: Validate ScanNet data structure before testing
    """
    print("=== Example 3: Data Validation ===")
    
    # Example data directory (replace with your actual path)
    data_dir = "/defaultShare/pubdata/3D/scannet-1500/LoFTR/testdata/scannet_test_1500/scannet_test_1500"
    
    print(f"Checking data structure in: {data_dir}")
    
    # Check data structure
    info = check_scannet_data_structure(data_dir)
    
    print(f"Data directory exists: {info['exists']}")
    print(f"Total scenes found: {info['total_scenes']}")
    print(f"Total images: {info['total_images']}")
    print(f"Has depth data: {info['has_depth']}")
    print(f"Has pose data: {info['has_poses']}")
    
    if info['structure_issues']:
        print("Structure issues found:")
        for issue in info['structure_issues']:
            print(f"  - {issue}")
    else:
        print("No structure issues found!")
    
    # Show details for first few scenes
    if info['scenes']:
        print("\nFirst 3 scenes details:")
        for i, scene in enumerate(info['scenes'][:3]):
            print(f"  Scene {i+1}: {scene['name']}")
            print(f"    Images: {scene['num_images']}")
            print(f"    Has color: {scene['has_color']}")
            print(f"    Has depth: {scene['has_depth']}")
            print(f"    Has poses: {scene['has_poses']}")
            print(f"    Has intrinsics: {scene['has_intrinsics']}")
    
    print("Data validation complete!\n")


def example_debug_mode():
    """
    Example 4: Debug mode for quick testing on a subset of data
    """
    print("=== Example 4: Debug Mode ===")
    
    # Load configuration
    with initialize(version_base=None, config_path="../training/config"):
        cfg = compose(config_name="scannet_test")
    
    # Enable debug mode
    cfg.data.val.common_config.debug = True
    cfg.limit_val_batches = 10  # Process only 10 batches
    cfg.max_img_per_gpu = 4     # Use smaller batch size
    
    # Reduce logging frequency
    cfg.logging.log_freq = 1
    cfg.logging.log_visual_frequency.val = 5
    
    print("Debug mode configuration:")
    print(f"  Debug enabled: {cfg.data.val.common_config.debug}")
    print(f"  Batch limit: {cfg.limit_val_batches}")
    print(f"  Max images per GPU: {cfg.max_img_per_gpu}")
    print(f"  Log frequency: {cfg.logging.log_freq}")
    
    from test_scannet import ScanNetTester
    tester = ScanNetTester(cfg, output_dir="./debug_scannet_results")
    
    print("Debug mode setup complete!\n")


def example_memory_optimization():
    """
    Example 5: Memory optimization for systems with limited GPU memory
    """
    print("=== Example 5: Memory Optimization ===")
    
    # Load configuration
    with initialize(version_base=None, config_path="../training/config"):
        cfg = compose(config_name="scannet_test")
    
    # Memory optimization settings
    cfg.max_img_per_gpu = 4  # Very small batch size
    cfg.data.val.dataset.dataset_configs[0].max_num_images = 6  # Fewer images per sequence
    cfg.data.val.dataset.dataset_configs[0].min_num_images = 4
    
    # Disable some memory-intensive features
    cfg.logging.log_visuals = False  # Disable visualization logging
    cfg.model.enable_point = False   # Disable point cloud prediction to save memory
    
    # Use mixed precision
    cfg.optim.amp.enabled = True
    cfg.optim.amp.amp_dtype = "float16"  # Use float16 instead of bfloat16
    
    print("Memory optimization settings:")
    print(f"  Max images per GPU: {cfg.max_img_per_gpu}")
    print(f"  Max images per sequence: {cfg.data.val.dataset.dataset_configs[0].max_num_images}")
    print(f"  Visual logging disabled: {not cfg.logging.log_visuals}")
    print(f"  Point cloud prediction disabled: {not cfg.model.enable_point}")
    print(f"  Mixed precision: {cfg.optim.amp.enabled} ({cfg.optim.amp.amp_dtype})")
    
    from test_scannet import ScanNetTester
    tester = ScanNetTester(cfg, output_dir="./memory_optimized_results")
    
    print("Memory optimization setup complete!\n")


def example_scene_selection():
    """
    Example 6: Test on specific scenes only
    """
    print("=== Example 6: Scene Selection ===")
    
    # Create a scene list file
    scene_list_file = "./selected_scenes.txt"
    selected_scenes = [
        "scene0000_00",
        "scene0001_00", 
        "scene0002_00"
    ]
    
    with open(scene_list_file, 'w') as f:
        for scene in selected_scenes:
            f.write(f"{scene}\n")
    
    print(f"Created scene list file: {scene_list_file}")
    print(f"Selected scenes: {selected_scenes}")
    
    # Load configuration
    with initialize(version_base=None, config_path="../training/config"):
        cfg = compose(config_name="scannet_test")
    
    # Set scene list file
    cfg.data.val.dataset.dataset_configs[0].scene_list_file = scene_list_file
    
    from test_scannet import ScanNetTester
    tester = ScanNetTester(cfg, output_dir="./selected_scenes_results")
    
    print("Scene selection setup complete!\n")
    
    # Clean up
    if os.path.exists(scene_list_file):
        os.remove(scene_list_file)


def main():
    """
    Main function to run all examples
    """
    parser = argparse.ArgumentParser(description="ScanNet Testing Examples")
    parser.add_argument(
        "--example", 
        type=int, 
        choices=[1, 2, 3, 4, 5, 6], 
        help="Run specific example (1-6)"
    )
    parser.add_argument(
        "--all", 
        action="store_true", 
        help="Run all examples"
    )
    
    args = parser.parse_args()
    
    examples = {
        1: example_basic_testing,
        2: example_custom_paths,
        3: example_data_validation,
        4: example_debug_mode,
        5: example_memory_optimization,
        6: example_scene_selection
    }
    
    if args.all:
        print("Running all ScanNet testing examples...\n")
        for i, example_func in examples.items():
            example_func()
    elif args.example:
        print(f"Running example {args.example}...\n")
        examples[args.example]()
    else:
        print("ScanNet Testing Examples")
        print("========================")
        print("Available examples:")
        print("  1. Basic testing with default configuration")
        print("  2. Custom paths and settings")
        print("  3. Data validation")
        print("  4. Debug mode for quick testing")
        print("  5. Memory optimization")
        print("  6. Scene selection")
        print("\nUsage:")
        print("  python scannet_test_example.py --example 1")
        print("  python scannet_test_example.py --all")


if __name__ == "__main__":
    main()
